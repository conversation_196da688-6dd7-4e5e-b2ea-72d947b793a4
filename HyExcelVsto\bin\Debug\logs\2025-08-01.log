﻿2025-08-01 00:06:39 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 00:06:39 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 00:06:39 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 00:06:39 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 00:06:39 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 7540078
2025-08-01 00:06:39 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-01 00:06:41 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-01 00:06:41 [INFO] 系统事件监控已停止
2025-08-01 00:06:41 [INFO] Excel窗口句柄监控已停止
2025-08-01 00:06:41 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-01 00:06:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:06:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8593570
2025-08-01 00:06:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8593570)
2025-08-01 00:06:43 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8593570
2025-08-01 00:06:43 [INFO] 系统事件监控已启动
2025-08-01 00:06:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:06:43 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 00:06:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:06:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8593570)
2025-08-01 00:06:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:06:43 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 00:06:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:06:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8593570
2025-08-01 00:06:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8593570)
2025-08-01 00:06:43 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:06:43 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 00:06:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:06:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8593570
2025-08-01 00:06:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8593570)
2025-08-01 00:06:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:06:44 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-01 00:06:44 [INFO] 系统事件监控已停止
2025-08-01 00:06:44 [INFO] Excel窗口句柄监控已停止
2025-08-01 00:06:44 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-01 00:06:44 [INFO] 开始VSTO插件关闭流程
2025-08-01 00:06:44 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250801_000644.txt
2025-08-01 00:06:44 [INFO] VSTO插件关闭流程完成
2025-08-01 00:07:14 [INFO] Excel窗口句柄监控器初始化完成
2025-08-01 00:07:15 [INFO] 配置文件实例已在加载时初始化
2025-08-01 00:07:15 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-01 00:07:15 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-01 00:07:15 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-01 00:07:15 [INFO] 成功初始化Excel应用程序实例
2025-08-01 00:07:15 [INFO] 自动备份路径未配置
2025-08-01 00:07:15 [DEBUG] 开始初始化授权控制器
2025-08-01 00:07:16 [DEBUG] 授权系统初始化完成，耗时: 592ms
2025-08-01 00:07:16 [DEBUG] 开始初始化授权验证
2025-08-01 00:07:16 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-01 00:07:16 [DEBUG] 权限管理器初始化成功
2025-08-01 00:07:16 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 00:07:16 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 00:07:16 [INFO] 开始初始化UI权限管理
2025-08-01 00:07:16 [DEBUG] [实例ID: 6b6e7fd8] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 00:07:16 [DEBUG] 🔍 [实例ID: 6b6e7fd8] 字典引用一致性检查:
2025-08-01 00:07:16 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 00:07:16 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 00:07:16 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 00:07:16 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 00:07:16 [DEBUG] 控件权限管理器初始化完成 [实例ID: 6b6e7fd8]
2025-08-01 00:07:16 [DEBUG] 开始注册控件权限映射
2025-08-01 00:07:16 [INFO] 开始初始化全局控件映射
2025-08-01 00:07:16 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-01 00:07:16 [DEBUG] 开始生成控件标题映射
2025-08-01 00:07:16 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 00:07:16 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 00:07:16 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 00:07:16 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-01 00:07:16 [INFO] 控件标题映射生成完成，共生成 108 项映射
2025-08-01 00:07:16 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:07:16 [DEBUG] 全局控件标题映射生成完成，共生成 108 项
2025-08-01 00:07:16 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-01 00:07:16 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-01 00:07:16 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-01 00:07:16 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-01 00:07:16 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-01 00:07:16 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-01 00:07:16 [INFO] === znAbout控件标题映射诊断 ===
2025-08-01 00:07:16 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-01 00:07:16 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-01 00:07:16 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-01 00:07:16 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-01 00:07:16 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-01 00:07:16 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-01 00:07:16 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-01 00:07:16 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-01 00:07:16 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-01 00:07:16 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-01 00:07:16 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-01 00:07:16 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-01 00:07:16 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-01 00:07:16 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-01 00:07:16 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-01 00:07:16 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-01 00:07:16 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-01 00:07:16 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-01 00:07:16 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-01 00:07:16 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-01 00:07:16 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-01 00:07:16 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-01 00:07:16 [DEBUG] 控件映射: button3 -> '关于'
2025-08-01 00:07:16 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-01 00:07:16 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-01 00:07:16 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-01 00:07:16 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-01 00:07:16 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-01 00:07:16 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-01 00:07:16 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-01 00:07:16 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-01 00:07:16 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-01 00:07:16 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-01 00:07:16 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-01 00:07:16 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-01 00:07:16 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-01 00:07:16 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-01 00:07:16 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-01 00:07:16 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-01 00:07:16 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-01 00:07:16 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-01 00:07:16 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-01 00:07:16 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-01 00:07:16 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-01 00:07:16 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-01 00:07:16 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-01 00:07:16 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-01 00:07:16 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-01 00:07:16 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-01 00:07:16 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-01 00:07:16 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-01 00:07:16 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-01 00:07:16 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-01 00:07:16 [DEBUG] 控件映射: group1 -> '关于'
2025-08-01 00:07:16 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-01 00:07:16 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-01 00:07:16 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-01 00:07:16 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-01 00:07:16 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-01 00:07:16 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-01 00:07:16 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-01 00:07:16 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-01 00:07:16 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-01 00:07:16 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-01 00:07:16 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-01 00:07:16 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-01 00:07:16 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-01 00:07:16 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-01 00:07:16 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-01 00:07:16 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-01 00:07:16 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-01 00:07:16 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-01 00:07:16 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-01 00:07:16 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-01 00:07:16 [DEBUG] 开始生成控件权限映射
2025-08-01 00:07:16 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 00:07:16 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 00:07:16 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 00:07:16 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 00:07:16 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 00:07:16 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:07:16 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 00:07:16 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:07:16 [INFO] 控件权限映射生成完成，共生成 115 项映射
2025-08-01 00:07:16 [DEBUG] 全局控件权限映射生成完成，共生成 115 项
2025-08-01 00:07:16 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-01 00:07:16 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-01 00:07:16 [INFO] 全局控件映射初始化完成 - 标题映射: 108 项, 权限映射: 115 项
2025-08-01 00:07:16 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 00:07:16 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 00:07:16 [INFO] 开始初始化权限验证
2025-08-01 00:07:16 [DEBUG] 设置默认UI可见性为false
2025-08-01 00:07:16 [DEBUG] 开始检查所有需要的权限
2025-08-01 00:07:16 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-01 00:07:17 [INFO] 启动网络授权信息获取任务
2025-08-01 00:07:17 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-01 00:07:17 [INFO] 所有权限检查完成
2025-08-01 00:07:17 [DEBUG] 应用权限状态到UI控件
2025-08-01 00:07:17 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:17 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:17 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:17 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:17 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:17 [DEBUG] 启动后台权限刷新任务
2025-08-01 00:07:17 [DEBUG] 启动延迟权限刷新任务
2025-08-01 00:07:17 [INFO] 权限验证初始化完成
2025-08-01 00:07:17 [INFO] UI权限管理初始化完成
2025-08-01 00:07:17 [INFO] 收到权限管理器初始化完成通知
2025-08-01 00:07:17 [INFO] 开始刷新控件标题
2025-08-01 00:07:17 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:07:17 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:07:17 [DEBUG] 控件标题刷新完成
2025-08-01 00:07:17 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:07:17 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:07:17 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:07:17 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:07:17 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:07:17 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:17 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:07:17 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:17 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:07:17 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:17 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:07:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:07:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:07:17 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:07:17 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:07:17 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:07:17 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:07:17 [INFO] 控件标题更正完成
2025-08-01 00:07:17 [INFO] 控件标题刷新完成
2025-08-01 00:07:17 [INFO] 权限管理器初始化完成处理结束
2025-08-01 00:07:17 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 00:07:17 [DEBUG] 授权验证初始化完成
2025-08-01 00:07:17 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-01 00:07:17 [INFO] 成功加载配置和授权信息
2025-08-01 00:07:17 [INFO] 开始初始化定时器和设置
2025-08-01 00:07:17 [INFO] 定时器和设置初始化完成
2025-08-01 00:07:17 [INFO] 开始VSTO插件启动流程
2025-08-01 00:07:17 [INFO] TopMostForm窗体加载完成
2025-08-01 00:07:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:07:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 82254048
2025-08-01 00:07:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 82254048)
2025-08-01 00:07:17 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 82254048
2025-08-01 00:07:17 [INFO] 系统事件监控已启动
2025-08-01 00:07:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:07:18 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-01 00:07:18 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-01 00:07:18 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-01 00:07:18 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-01 00:07:18 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 102046404
2025-08-01 00:07:18 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-01 00:07:18 [INFO] VSTO插件启动流程完成
2025-08-01 00:07:18 [INFO] 从Remote成功获取到网络授权信息
2025-08-01 00:07:18 [INFO] 网络授权信息已更新并触发回调
2025-08-01 00:07:18 [INFO] 网络授权信息已从 Network 更新
2025-08-01 00:07:18 [INFO] 授权版本: 1.0
2025-08-01 00:07:18 [INFO] 颁发者: ExtensionsTools
2025-08-01 00:07:18 [INFO] 用户数量: 3
2025-08-01 00:07:18 [INFO] 分组权限数量: 2
2025-08-01 00:07:18 [WARN] 配置文件中未找到用户组信息
2025-08-01 00:07:18 [INFO] 已重新设置用户组: []
2025-08-01 00:07:18 [INFO] 用户组信息已重新设置
2025-08-01 00:07:18 [INFO] 立即刷新权限缓存和UI界面
2025-08-01 00:07:18 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 00:07:18 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-01 00:07:18 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-01 00:07:18 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 00:07:18 [DEBUG] 本地权限缓存已清空
2025-08-01 00:07:18 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-01 00:07:19 [INFO] 所有权限检查完成
2025-08-01 00:07:19 [DEBUG] 权限重新检查完成
2025-08-01 00:07:19 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:19 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:19 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:19 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:19 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:19 [INFO] UI界面权限状态已更新
2025-08-01 00:07:19 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:07:19 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:07:19 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-01 00:07:19 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-01 00:07:19 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-01 00:07:19 [INFO] 开始刷新Ribbon控件标题
2025-08-01 00:07:19 [DEBUG] 权限缓存已清空，清除了 115 个缓存项
2025-08-01 00:07:19 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-01 00:07:19 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:07:19 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:07:19 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:19 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:07:19 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:07:19 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:07:19 [INFO] 控件标题更正完成
2025-08-01 00:07:19 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-01 00:07:19 [INFO] Ribbon控件标题刷新完成
2025-08-01 00:07:19 [INFO] 控件标题刷新完成
2025-08-01 00:07:19 [DEBUG] Ribbon控件标题已刷新
2025-08-01 00:07:19 [INFO] 开始刷新控件标题
2025-08-01 00:07:19 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:07:19 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:07:19 [DEBUG] 控件标题刷新完成
2025-08-01 00:07:19 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:07:19 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:07:19 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:07:19 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:19 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:07:19 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:19 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:07:19 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:07:19 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:07:19 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:07:19 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:07:19 [INFO] 控件标题更正完成
2025-08-01 00:07:19 [INFO] 控件标题刷新完成
2025-08-01 00:07:19 [DEBUG] Ribbon控件标题已立即刷新
2025-08-01 00:07:19 [INFO] 开始刷新授权状态
2025-08-01 00:07:19 [DEBUG] 开始初始化授权验证
2025-08-01 00:07:19 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 00:07:19 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 00:07:19 [INFO] 开始初始化UI权限管理
2025-08-01 00:07:19 [DEBUG] [实例ID: b8e54ff7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 00:07:19 [DEBUG] 🔍 [实例ID: b8e54ff7] 字典引用一致性检查:
2025-08-01 00:07:19 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 00:07:19 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 00:07:19 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 00:07:19 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 00:07:19 [DEBUG] 控件权限管理器初始化完成 [实例ID: b8e54ff7]
2025-08-01 00:07:19 [DEBUG] 开始注册控件权限映射
2025-08-01 00:07:19 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 00:07:19 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 00:07:19 [INFO] 开始初始化权限验证
2025-08-01 00:07:19 [DEBUG] 设置默认UI可见性为false
2025-08-01 00:07:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:07:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 82254048
2025-08-01 00:07:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 82254048)
2025-08-01 00:07:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:07:20 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 00:07:20 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:20 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:20 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:20 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:20 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:20 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:20 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:20 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:20 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:20 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:20 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:20 [DEBUG] 开始重置 209 个命令栏
2025-08-01 00:07:21 [DEBUG] 开始检查所有需要的权限
2025-08-01 00:07:21 [INFO] 所有权限检查完成
2025-08-01 00:07:21 [DEBUG] 应用权限状态到UI控件
2025-08-01 00:07:21 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:21 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:21 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:21 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:21 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:21 [DEBUG] 启动后台权限刷新任务
2025-08-01 00:07:21 [DEBUG] 启动延迟权限刷新任务
2025-08-01 00:07:21 [INFO] 权限验证初始化完成
2025-08-01 00:07:21 [INFO] UI权限管理初始化完成
2025-08-01 00:07:21 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:21 [INFO] 收到权限管理器初始化完成通知
2025-08-01 00:07:21 [INFO] 开始刷新控件标题
2025-08-01 00:07:21 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:21 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:07:21 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:21 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:07:21 [DEBUG] 控件标题刷新完成
2025-08-01 00:07:21 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:07:21 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:07:21 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:21 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:21 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:21 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:07:21 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:07:21 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:21 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:07:21 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:07:21 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:07:21 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:07:21 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:07:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:07:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:07:21 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:07:21 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:07:21 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:07:21 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:07:21 [INFO] 控件标题更正完成
2025-08-01 00:07:21 [INFO] 控件标题刷新完成
2025-08-01 00:07:21 [INFO] 权限管理器初始化完成处理结束
2025-08-01 00:07:21 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 00:07:21 [DEBUG] 授权验证初始化完成
2025-08-01 00:07:21 [INFO] 授权状态刷新完成
2025-08-01 00:07:21 [DEBUG] 重置命令栏: cell
2025-08-01 00:07:21 [DEBUG] 重置命令栏: column
2025-08-01 00:07:21 [DEBUG] 重置命令栏: row
2025-08-01 00:07:21 [DEBUG] 重置命令栏: cell
2025-08-01 00:07:21 [DEBUG] 重置命令栏: column
2025-08-01 00:07:21 [DEBUG] 重置命令栏: row
2025-08-01 00:07:21 [DEBUG] 重置命令栏: row
2025-08-01 00:07:21 [DEBUG] 重置命令栏: column
2025-08-01 00:07:23 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:07:23 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:07:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:23 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:07:23 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:07:23 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:07:23 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:07:23 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-01 00:07:27 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:27 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:27 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:28 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:28 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:28 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:28 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:29 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:30 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:30 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:30 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:31 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:31 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:31 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:31 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:32 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:33 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:33 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:33 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:34 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:34 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:34 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:34 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:35 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:35 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:35 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:35 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:36 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:36 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:07:36 [DEBUG] 授权控制器已初始化
2025-08-01 00:07:36 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:07:37 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:07:38 [DEBUG] 已重置工作表标签菜单
2025-08-01 00:07:38 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:00 [INFO] Excel窗口句柄监控器初始化完成
2025-08-01 00:08:00 [INFO] 配置文件实例已在加载时初始化
2025-08-01 00:08:00 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-01 00:08:00 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-01 00:08:00 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-01 00:08:00 [INFO] 成功初始化Excel应用程序实例
2025-08-01 00:08:00 [INFO] 自动备份路径未配置
2025-08-01 00:08:00 [DEBUG] 开始初始化授权控制器
2025-08-01 00:08:01 [DEBUG] 授权系统初始化完成，耗时: 569ms
2025-08-01 00:08:01 [DEBUG] 开始初始化授权验证
2025-08-01 00:08:01 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-01 00:08:01 [DEBUG] 权限管理器初始化成功
2025-08-01 00:08:01 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 00:08:01 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 00:08:01 [INFO] 开始初始化UI权限管理
2025-08-01 00:08:01 [DEBUG] [实例ID: 45272c8e] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 00:08:01 [DEBUG] 🔍 [实例ID: 45272c8e] 字典引用一致性检查:
2025-08-01 00:08:01 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 00:08:01 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 00:08:01 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 00:08:01 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 00:08:01 [DEBUG] 控件权限管理器初始化完成 [实例ID: 45272c8e]
2025-08-01 00:08:01 [DEBUG] 开始注册控件权限映射
2025-08-01 00:08:01 [INFO] 开始初始化全局控件映射
2025-08-01 00:08:01 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-01 00:08:01 [DEBUG] 开始生成控件标题映射
2025-08-01 00:08:01 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 00:08:01 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 00:08:01 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 00:08:01 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-01 00:08:01 [INFO] 控件标题映射生成完成，共生成 108 项映射
2025-08-01 00:08:01 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:08:01 [DEBUG] 全局控件标题映射生成完成，共生成 108 项
2025-08-01 00:08:01 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-01 00:08:01 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-01 00:08:01 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-01 00:08:01 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-01 00:08:01 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-01 00:08:01 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-01 00:08:01 [INFO] === znAbout控件标题映射诊断 ===
2025-08-01 00:08:01 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-01 00:08:01 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-01 00:08:01 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-01 00:08:01 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-01 00:08:01 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-01 00:08:01 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-01 00:08:01 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-01 00:08:01 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-01 00:08:01 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-01 00:08:01 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-01 00:08:01 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-01 00:08:01 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-01 00:08:01 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-01 00:08:01 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-01 00:08:01 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-01 00:08:01 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-01 00:08:01 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-01 00:08:01 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-01 00:08:01 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-01 00:08:01 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-01 00:08:01 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-01 00:08:01 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-01 00:08:01 [DEBUG] 控件映射: button3 -> '关于'
2025-08-01 00:08:01 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-01 00:08:01 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-01 00:08:01 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-01 00:08:01 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-01 00:08:01 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-01 00:08:01 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-01 00:08:01 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-01 00:08:01 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-01 00:08:01 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-01 00:08:01 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-01 00:08:01 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-01 00:08:01 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-01 00:08:01 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-01 00:08:01 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-01 00:08:01 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-01 00:08:01 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-01 00:08:01 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-01 00:08:01 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-01 00:08:01 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-01 00:08:01 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-01 00:08:01 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-01 00:08:01 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-01 00:08:01 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-01 00:08:01 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-01 00:08:01 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-01 00:08:01 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-01 00:08:01 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-01 00:08:01 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-01 00:08:01 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-01 00:08:01 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-01 00:08:01 [DEBUG] 控件映射: group1 -> '关于'
2025-08-01 00:08:01 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-01 00:08:01 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-01 00:08:01 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-01 00:08:01 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-01 00:08:01 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-01 00:08:01 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-01 00:08:01 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-01 00:08:01 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-01 00:08:01 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-01 00:08:01 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-01 00:08:01 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-01 00:08:01 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-01 00:08:01 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-01 00:08:01 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-01 00:08:01 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-01 00:08:01 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-01 00:08:01 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-01 00:08:01 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-01 00:08:01 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-01 00:08:01 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-01 00:08:01 [DEBUG] 开始生成控件权限映射
2025-08-01 00:08:01 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 00:08:01 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 00:08:01 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 00:08:01 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 00:08:01 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 00:08:01 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 00:08:01 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 00:08:01 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 00:08:01 [INFO] 控件权限映射生成完成，共生成 115 项映射
2025-08-01 00:08:01 [DEBUG] 全局控件权限映射生成完成，共生成 115 项
2025-08-01 00:08:01 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-01 00:08:01 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-01 00:08:01 [INFO] 全局控件映射初始化完成 - 标题映射: 108 项, 权限映射: 115 项
2025-08-01 00:08:01 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 00:08:01 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 00:08:01 [INFO] 开始初始化权限验证
2025-08-01 00:08:01 [DEBUG] 设置默认UI可见性为false
2025-08-01 00:08:01 [DEBUG] 开始检查所有需要的权限
2025-08-01 00:08:01 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-01 00:08:02 [INFO] 启动网络授权信息获取任务
2025-08-01 00:08:02 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-01 00:08:02 [INFO] 所有权限检查完成
2025-08-01 00:08:02 [DEBUG] 应用权限状态到UI控件
2025-08-01 00:08:02 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:02 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:02 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:02 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:02 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:02 [DEBUG] 启动后台权限刷新任务
2025-08-01 00:08:02 [DEBUG] 启动延迟权限刷新任务
2025-08-01 00:08:02 [INFO] 权限验证初始化完成
2025-08-01 00:08:02 [INFO] UI权限管理初始化完成
2025-08-01 00:08:02 [INFO] 收到权限管理器初始化完成通知
2025-08-01 00:08:02 [INFO] 开始刷新控件标题
2025-08-01 00:08:02 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:08:02 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:08:02 [DEBUG] 控件标题刷新完成
2025-08-01 00:08:02 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:08:02 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:08:02 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:08:02 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:08:02 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:08:02 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:08:02 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:02 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:08:02 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:02 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:08:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:08:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:08:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:08:02 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:08:02 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:08:02 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:08:02 [INFO] 控件标题更正完成
2025-08-01 00:08:02 [INFO] 控件标题刷新完成
2025-08-01 00:08:02 [INFO] 权限管理器初始化完成处理结束
2025-08-01 00:08:02 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 00:08:02 [DEBUG] 授权验证初始化完成
2025-08-01 00:08:02 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-01 00:08:02 [INFO] 成功加载配置和授权信息
2025-08-01 00:08:02 [INFO] 开始初始化定时器和设置
2025-08-01 00:08:02 [INFO] 定时器和设置初始化完成
2025-08-01 00:08:02 [INFO] 开始VSTO插件启动流程
2025-08-01 00:08:02 [INFO] TopMostForm窗体加载完成
2025-08-01 00:08:02 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:08:02 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 19142738
2025-08-01 00:08:02 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 19142738)
2025-08-01 00:08:02 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 19142738
2025-08-01 00:08:02 [INFO] 系统事件监控已启动
2025-08-01 00:08:02 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:08:02 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-01 00:08:02 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-01 00:08:02 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-01 00:08:02 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-01 00:08:02 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 4923632
2025-08-01 00:08:02 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-01 00:08:02 [INFO] VSTO插件启动流程完成
2025-08-01 00:08:03 [INFO] 从Remote成功获取到网络授权信息
2025-08-01 00:08:03 [INFO] 网络授权信息已更新并触发回调
2025-08-01 00:08:03 [INFO] 网络授权信息已从 Network 更新
2025-08-01 00:08:03 [INFO] 授权版本: 1.0
2025-08-01 00:08:03 [INFO] 颁发者: ExtensionsTools
2025-08-01 00:08:03 [INFO] 用户数量: 3
2025-08-01 00:08:03 [INFO] 分组权限数量: 2
2025-08-01 00:08:03 [WARN] 配置文件中未找到用户组信息
2025-08-01 00:08:03 [INFO] 已重新设置用户组: []
2025-08-01 00:08:03 [INFO] 用户组信息已重新设置
2025-08-01 00:08:03 [INFO] 立即刷新权限缓存和UI界面
2025-08-01 00:08:03 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 00:08:03 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-01 00:08:03 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-01 00:08:03 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 00:08:03 [DEBUG] 本地权限缓存已清空
2025-08-01 00:08:03 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-01 00:08:03 [INFO] 所有权限检查完成
2025-08-01 00:08:03 [DEBUG] 权限重新检查完成
2025-08-01 00:08:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:03 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:03 [INFO] UI界面权限状态已更新
2025-08-01 00:08:03 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:08:03 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:08:03 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-01 00:08:03 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-01 00:08:03 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-01 00:08:03 [INFO] 开始刷新Ribbon控件标题
2025-08-01 00:08:03 [DEBUG] 权限缓存已清空，清除了 115 个缓存项
2025-08-01 00:08:03 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-01 00:08:03 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:08:03 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:08:03 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:03 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:08:03 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:08:03 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:08:03 [INFO] 控件标题更正完成
2025-08-01 00:08:03 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-01 00:08:03 [INFO] Ribbon控件标题刷新完成
2025-08-01 00:08:03 [INFO] 控件标题刷新完成
2025-08-01 00:08:03 [DEBUG] Ribbon控件标题已刷新
2025-08-01 00:08:03 [INFO] 开始刷新控件标题
2025-08-01 00:08:03 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:08:03 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:08:03 [DEBUG] 控件标题刷新完成
2025-08-01 00:08:03 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:08:03 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:08:03 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:08:03 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:03 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:08:03 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:03 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:08:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:08:03 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:08:03 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:08:03 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:08:03 [INFO] 控件标题更正完成
2025-08-01 00:08:03 [INFO] 控件标题刷新完成
2025-08-01 00:08:03 [DEBUG] Ribbon控件标题已立即刷新
2025-08-01 00:08:03 [INFO] 开始刷新授权状态
2025-08-01 00:08:03 [DEBUG] 开始初始化授权验证
2025-08-01 00:08:03 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 00:08:03 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 00:08:03 [INFO] 开始初始化UI权限管理
2025-08-01 00:08:03 [DEBUG] [实例ID: 1160d653] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 00:08:03 [DEBUG] 🔍 [实例ID: 1160d653] 字典引用一致性检查:
2025-08-01 00:08:03 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 00:08:03 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 00:08:03 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 00:08:03 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 00:08:03 [DEBUG] 控件权限管理器初始化完成 [实例ID: 1160d653]
2025-08-01 00:08:03 [DEBUG] 开始注册控件权限映射
2025-08-01 00:08:03 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 00:08:03 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 00:08:03 [INFO] 开始初始化权限验证
2025-08-01 00:08:03 [DEBUG] 设置默认UI可见性为false
2025-08-01 00:08:04 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:08:04 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 19142738
2025-08-01 00:08:04 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 19142738)
2025-08-01 00:08:04 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:08:04 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 00:08:04 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:04 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:04 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:04 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:04 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:04 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:04 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:04 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:04 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:04 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:04 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:04 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:04 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:04 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:04 [DEBUG] 开始重置 209 个命令栏
2025-08-01 00:08:05 [DEBUG] 开始检查所有需要的权限
2025-08-01 00:08:05 [INFO] 所有权限检查完成
2025-08-01 00:08:05 [DEBUG] 应用权限状态到UI控件
2025-08-01 00:08:05 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:05 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:05 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:05 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:05 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:05 [DEBUG] 启动后台权限刷新任务
2025-08-01 00:08:05 [DEBUG] 启动延迟权限刷新任务
2025-08-01 00:08:05 [INFO] 权限验证初始化完成
2025-08-01 00:08:05 [INFO] UI权限管理初始化完成
2025-08-01 00:08:05 [INFO] 收到权限管理器初始化完成通知
2025-08-01 00:08:05 [INFO] 开始刷新控件标题
2025-08-01 00:08:05 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:05 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:05 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:05 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:05 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:05 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 00:08:05 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 00:08:05 [DEBUG] 控件标题刷新完成
2025-08-01 00:08:05 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 00:08:05 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 00:08:05 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 00:08:05 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:05 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 00:08:05 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:05 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 00:08:05 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:05 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 00:08:05 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 00:08:05 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 00:08:05 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 00:08:05 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 00:08:05 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 00:08:05 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 00:08:05 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 00:08:05 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 00:08:05 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 00:08:05 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 00:08:05 [INFO] 控件标题更正完成
2025-08-01 00:08:05 [INFO] 控件标题刷新完成
2025-08-01 00:08:05 [INFO] 权限管理器初始化完成处理结束
2025-08-01 00:08:05 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 00:08:05 [DEBUG] 授权验证初始化完成
2025-08-01 00:08:05 [INFO] 授权状态刷新完成
2025-08-01 00:08:05 [DEBUG] 重置命令栏: cell
2025-08-01 00:08:05 [DEBUG] 重置命令栏: column
2025-08-01 00:08:05 [DEBUG] 重置命令栏: row
2025-08-01 00:08:05 [DEBUG] 重置命令栏: cell
2025-08-01 00:08:05 [DEBUG] 重置命令栏: column
2025-08-01 00:08:05 [DEBUG] 重置命令栏: row
2025-08-01 00:08:05 [DEBUG] 重置命令栏: row
2025-08-01 00:08:05 [DEBUG] 重置命令栏: column
2025-08-01 00:08:06 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-01 00:08:07 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 00:08:07 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 00:08:07 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:07 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 00:08:07 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 00:08:07 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 00:08:07 [DEBUG] 已应用权限状态到UI控件
2025-08-01 00:08:09 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:09 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:09 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:13 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:14 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:14 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:14 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:15 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:16 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:16 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:16 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:17 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:18 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:20 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:21 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:21 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:21 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:23 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:23 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:23 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:24 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:24 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:24 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:25 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:25 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:08:25 [DEBUG] 授权控制器已初始化
2025-08-01 00:08:25 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 00:08:26 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 00:08:27 [DEBUG] 已重置工作表标签菜单
2025-08-01 00:08:27 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 00:14:37 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 00:14:37 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 00:14:37 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 00:14:37 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 00:14:37 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 6626898
2025-08-01 00:14:37 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-01 00:14:42 [WARN] 检测到Excel窗口句柄变化: 19142738 -> 16256916
2025-08-01 00:14:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 19142738, 新父窗口: 16256916
2025-08-01 00:14:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 00:14:43 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开事件触发
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:14:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:14:43 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开处理完成
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:14:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:14:43 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 00:14:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 00:14:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 00:14:43 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 00:14:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:14:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 00:14:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 00:14:43 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:14:43 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 00:14:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:14:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 00:14:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 00:14:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:14:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 00:14:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 00:14:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 00:14:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 00:14:44 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-01 07:29:49 [INFO] 显示设置已变更
2025-08-01 07:29:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 07:29:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 07:29:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 07:29:50 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 07:29:50 [INFO] 显示设置变更后TopForm关系已重建
2025-08-01 07:30:12 [INFO] 显示设置已变更
2025-08-01 07:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 07:30:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 07:30:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 07:30:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 07:30:13 [INFO] 显示设置变更后TopForm关系已重建
2025-08-01 08:39:27 [INFO] 会话切换事件: ConsoleDisconnect
2025-08-01 08:39:29 [INFO] 会话切换事件: SessionLock
2025-08-01 08:39:30 [INFO] 会话切换事件: RemoteConnect
2025-08-01 08:39:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:39:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:39:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:39:33 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:39:33 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:39:36 [INFO] 会话切换事件: SessionUnlock
2025-08-01 08:39:36 [INFO] 显示设置已变更
2025-08-01 08:39:37 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:39:37 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:39:38 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:39:38 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:39:38 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:39:38 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:39:38 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:39:38 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:39:38 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:39:38 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:39:38 [INFO] 显示设置变更后TopForm关系已重建
2025-08-01 08:39:38 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:39:38 [INFO] 会话恢复后TopForm关系已重建
2025-08-01 08:40:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:40:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16256916, 新父窗口: 16064208
2025-08-01 08:40:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:40:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:40:16 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-01 08:40:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:40:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:40:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:40:16 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:40:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:40:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:40:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:40:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:40:17 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:40:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:40:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:40:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:40:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:40:21 [INFO] OpenForm: 窗体标题已设置为类名 '51Helper'
2025-08-01 08:40:21 [INFO] OpenForm: 准备打开窗体 '51Helper'，位置: Center，单实例: True
2025-08-01 08:40:21 [INFO] 开始显示窗体 '51Helper'，位置模式: Center
2025-08-01 08:40:22 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 开始获取用户信息
2025-08-01 08:40:23 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 用户信息获取成功
2025-08-01 08:40:23 [INFO] 窗体 '51Helper' 以TopMostForm为父窗体显示
2025-08-01 08:40:23 [INFO] 窗体 '51Helper' 显示完成，句柄: 4859646
2025-08-01 08:40:23 [INFO] OpenForm: 窗体 '51Helper' 打开成功
2025-08-01 08:49:10 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 开始获取用户信息
2025-08-01 08:49:10 [ERROR] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 获取用户信息失败
2025-08-01 08:49:10 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 用户信息获取成功
2025-08-01 08:50:24 [INFO] OpenForm: 窗体标题已设置为类名 '51Helper'
2025-08-01 08:50:24 [INFO] OpenForm: 准备打开窗体 '51Helper'，位置: Center，单实例: True
2025-08-01 08:50:24 [INFO] 开始显示窗体 '51Helper'，位置模式: Center
2025-08-01 08:50:25 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 开始获取用户信息
2025-08-01 08:50:25 [DEBUG] [HyExcelVsto.Extensions.Dx51Helper.Zn51Helper] 用户信息获取成功
2025-08-01 08:50:25 [INFO] 窗体 '51Helper' 以TopMostForm为父窗体显示
2025-08-01 08:50:25 [INFO] 窗体 '51Helper' 显示完成，句柄: 156441028
2025-08-01 08:50:25 [INFO] OpenForm: 窗体 '51Helper' 打开成功
2025-08-01 08:51:03 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:51:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16064208, 新父窗口: 16256916
2025-08-01 08:51:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:51:03 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:51:03 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:51:03 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:51:03 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:51:03 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:51:03 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:51:04 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:51:04 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:51:04 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:51:04 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:51:04 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:51:04 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:51:04 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:51:04 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:51:04 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:51:36 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:36 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:36 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:38 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:38 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:38 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:39 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:39 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:39 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:40 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:40 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:40 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:40 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:40 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:40 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:40 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:40 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:40 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:40 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:40 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:40 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:41 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:41 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:41 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:43 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:43 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:43 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:44 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:44 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:44 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:51:47 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:51:47 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:51:47 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:52:22 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:52:22 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:52:22 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:52:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:52:43 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16256916, 新父窗口: 16064208
2025-08-01 08:52:43 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:52:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:52:43 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-01 08:52:43 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:52:43 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:52:43 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:52:43 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:52:43 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:52:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:52:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:52:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:52:44 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:52:44 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:52:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:52:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:52:44 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:52:44 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:52:44 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:52:44 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:52:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:52:46 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:52:46 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:52:46 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:52:46 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:52:50 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:52:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16064208, 新父窗口: 16256916
2025-08-01 08:52:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:52:50 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:52:50 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:52:50 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:52:50 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:52:50 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:52:50 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:52:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:52:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:52:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:52:51 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:52:51 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:52:51 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:52:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:52:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:52:51 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:53:12 [INFO] Excel窗口句柄监控器初始化完成
2025-08-01 08:53:13 [INFO] 配置文件实例已在加载时初始化
2025-08-01 08:53:13 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-01 08:53:13 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-01 08:53:13 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-01 08:53:13 [INFO] 成功初始化Excel应用程序实例
2025-08-01 08:53:13 [INFO] 自动备份路径未配置
2025-08-01 08:53:13 [DEBUG] 开始初始化授权控制器
2025-08-01 08:53:13 [DEBUG] 授权系统初始化完成，耗时: 434ms
2025-08-01 08:53:13 [DEBUG] 开始初始化授权验证
2025-08-01 08:53:13 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-01 08:53:13 [DEBUG] 权限管理器初始化成功
2025-08-01 08:53:13 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 08:53:13 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 08:53:13 [INFO] 开始初始化UI权限管理
2025-08-01 08:53:13 [DEBUG] [实例ID: e9a0a2d7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 08:53:13 [DEBUG] 🔍 [实例ID: e9a0a2d7] 字典引用一致性检查:
2025-08-01 08:53:13 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 08:53:13 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 08:53:13 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 08:53:13 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 08:53:13 [DEBUG] 控件权限管理器初始化完成 [实例ID: e9a0a2d7]
2025-08-01 08:53:13 [DEBUG] 开始注册控件权限映射
2025-08-01 08:53:13 [INFO] 开始初始化全局控件映射
2025-08-01 08:53:13 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-01 08:53:13 [DEBUG] 开始生成控件标题映射
2025-08-01 08:53:13 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 08:53:13 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 08:53:13 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 08:53:13 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-01 08:53:13 [INFO] 控件标题映射生成完成，共生成 108 项映射
2025-08-01 08:53:13 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:53:13 [DEBUG] 全局控件标题映射生成完成，共生成 108 项
2025-08-01 08:53:13 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-01 08:53:13 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-01 08:53:13 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-01 08:53:13 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-01 08:53:13 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-01 08:53:13 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-01 08:53:13 [INFO] === znAbout控件标题映射诊断 ===
2025-08-01 08:53:13 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-01 08:53:13 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-01 08:53:13 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-01 08:53:13 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-01 08:53:13 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-01 08:53:13 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-01 08:53:13 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-01 08:53:13 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-01 08:53:13 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-01 08:53:13 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-01 08:53:13 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-01 08:53:13 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-01 08:53:13 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-01 08:53:13 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-01 08:53:13 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-01 08:53:13 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-01 08:53:13 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-01 08:53:13 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-01 08:53:13 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-01 08:53:13 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-01 08:53:13 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-01 08:53:13 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-01 08:53:13 [DEBUG] 控件映射: button3 -> '关于'
2025-08-01 08:53:13 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-01 08:53:13 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-01 08:53:13 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-01 08:53:13 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-01 08:53:13 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-01 08:53:13 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-01 08:53:13 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-01 08:53:13 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-01 08:53:13 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-01 08:53:13 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-01 08:53:13 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-01 08:53:13 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-01 08:53:13 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-01 08:53:13 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-01 08:53:13 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-01 08:53:13 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-01 08:53:13 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-01 08:53:13 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-01 08:53:13 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-01 08:53:13 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-01 08:53:13 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-01 08:53:13 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-01 08:53:13 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-01 08:53:13 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-01 08:53:13 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-01 08:53:13 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-01 08:53:13 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-01 08:53:13 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-01 08:53:13 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-01 08:53:13 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-01 08:53:13 [DEBUG] 控件映射: group1 -> '关于'
2025-08-01 08:53:13 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-01 08:53:13 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-01 08:53:13 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-01 08:53:13 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-01 08:53:13 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-01 08:53:13 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-01 08:53:13 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-01 08:53:13 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-01 08:53:13 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-01 08:53:13 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-01 08:53:13 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-01 08:53:13 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-01 08:53:13 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-01 08:53:13 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-01 08:53:13 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-01 08:53:13 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-01 08:53:13 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-01 08:53:13 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-01 08:53:13 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-01 08:53:13 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-01 08:53:13 [DEBUG] 开始生成控件权限映射
2025-08-01 08:53:13 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 08:53:13 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 08:53:13 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 08:53:13 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 08:53:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 08:53:13 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:53:13 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 08:53:13 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:53:13 [INFO] 控件权限映射生成完成，共生成 115 项映射
2025-08-01 08:53:13 [DEBUG] 全局控件权限映射生成完成，共生成 115 项
2025-08-01 08:53:13 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-01 08:53:13 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-01 08:53:13 [INFO] 全局控件映射初始化完成 - 标题映射: 108 项, 权限映射: 115 项
2025-08-01 08:53:13 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 08:53:13 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 08:53:13 [INFO] 开始初始化权限验证
2025-08-01 08:53:13 [DEBUG] 设置默认UI可见性为false
2025-08-01 08:53:13 [DEBUG] 开始检查所有需要的权限
2025-08-01 08:53:13 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-01 08:53:14 [INFO] 启动网络授权信息获取任务
2025-08-01 08:53:14 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-01 08:53:14 [INFO] 所有权限检查完成
2025-08-01 08:53:14 [DEBUG] 应用权限状态到UI控件
2025-08-01 08:53:14 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:14 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:14 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:14 [DEBUG] 启动后台权限刷新任务
2025-08-01 08:53:14 [DEBUG] 启动延迟权限刷新任务
2025-08-01 08:53:14 [INFO] 权限验证初始化完成
2025-08-01 08:53:14 [INFO] UI权限管理初始化完成
2025-08-01 08:53:14 [INFO] 收到权限管理器初始化完成通知
2025-08-01 08:53:14 [INFO] 开始刷新控件标题
2025-08-01 08:53:14 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:53:14 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:53:14 [DEBUG] 控件标题刷新完成
2025-08-01 08:53:14 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:53:14 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:53:14 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:53:14 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:53:14 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:53:14 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:53:14 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:14 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:53:14 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:14 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:53:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:53:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:53:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:53:14 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:53:14 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:53:14 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:53:14 [INFO] 控件标题更正完成
2025-08-01 08:53:14 [INFO] 控件标题刷新完成
2025-08-01 08:53:14 [INFO] 权限管理器初始化完成处理结束
2025-08-01 08:53:14 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 08:53:14 [DEBUG] 授权验证初始化完成
2025-08-01 08:53:14 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-01 08:53:14 [INFO] 成功加载配置和授权信息
2025-08-01 08:53:14 [INFO] 开始初始化定时器和设置
2025-08-01 08:53:14 [INFO] 定时器和设置初始化完成
2025-08-01 08:53:14 [INFO] 开始VSTO插件启动流程
2025-08-01 08:53:14 [INFO] TopMostForm窗体加载完成
2025-08-01 08:53:14 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:53:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 19732074
2025-08-01 08:53:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 19732074)
2025-08-01 08:53:14 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 19732074
2025-08-01 08:53:14 [INFO] 系统事件监控已启动
2025-08-01 08:53:14 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:53:14 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-01 08:53:14 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-01 08:53:14 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-01 08:53:14 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-01 08:53:14 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 24577066
2025-08-01 08:53:14 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-01 08:53:14 [INFO] VSTO插件启动流程完成
2025-08-01 08:53:15 [INFO] 从Remote成功获取到网络授权信息
2025-08-01 08:53:15 [INFO] 网络授权信息已更新并触发回调
2025-08-01 08:53:15 [INFO] 网络授权信息已从 Network 更新
2025-08-01 08:53:15 [INFO] 授权版本: 1.0
2025-08-01 08:53:15 [INFO] 颁发者: ExtensionsTools
2025-08-01 08:53:15 [INFO] 用户数量: 3
2025-08-01 08:53:15 [INFO] 分组权限数量: 2
2025-08-01 08:53:15 [WARN] 配置文件中未找到用户组信息
2025-08-01 08:53:15 [INFO] 已重新设置用户组: []
2025-08-01 08:53:15 [INFO] 用户组信息已重新设置
2025-08-01 08:53:15 [INFO] 立即刷新权限缓存和UI界面
2025-08-01 08:53:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 08:53:15 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-01 08:53:15 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-01 08:53:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 08:53:15 [DEBUG] 本地权限缓存已清空
2025-08-01 08:53:15 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-01 08:53:15 [INFO] 所有权限检查完成
2025-08-01 08:53:15 [DEBUG] 权限重新检查完成
2025-08-01 08:53:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:15 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:15 [INFO] UI界面权限状态已更新
2025-08-01 08:53:15 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:53:15 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:53:15 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-01 08:53:15 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-01 08:53:15 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-01 08:53:15 [INFO] 开始刷新Ribbon控件标题
2025-08-01 08:53:15 [DEBUG] 权限缓存已清空，清除了 115 个缓存项
2025-08-01 08:53:15 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-01 08:53:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:53:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:53:15 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:15 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:53:15 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:53:15 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:53:15 [INFO] 控件标题更正完成
2025-08-01 08:53:15 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-01 08:53:15 [INFO] Ribbon控件标题刷新完成
2025-08-01 08:53:15 [INFO] 控件标题刷新完成
2025-08-01 08:53:15 [DEBUG] Ribbon控件标题已刷新
2025-08-01 08:53:15 [INFO] 开始刷新控件标题
2025-08-01 08:53:15 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:53:15 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:53:15 [DEBUG] 控件标题刷新完成
2025-08-01 08:53:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:53:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:53:15 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:53:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:15 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:53:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:15 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:53:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:53:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:53:15 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:53:15 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:53:15 [INFO] 控件标题更正完成
2025-08-01 08:53:15 [INFO] 控件标题刷新完成
2025-08-01 08:53:15 [DEBUG] Ribbon控件标题已立即刷新
2025-08-01 08:53:15 [INFO] 开始刷新授权状态
2025-08-01 08:53:15 [DEBUG] 开始初始化授权验证
2025-08-01 08:53:15 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 08:53:15 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 08:53:15 [INFO] 开始初始化UI权限管理
2025-08-01 08:53:15 [DEBUG] [实例ID: a3d83920] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 08:53:15 [DEBUG] 🔍 [实例ID: a3d83920] 字典引用一致性检查:
2025-08-01 08:53:15 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 08:53:15 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 08:53:15 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 08:53:15 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 08:53:15 [DEBUG] 控件权限管理器初始化完成 [实例ID: a3d83920]
2025-08-01 08:53:15 [DEBUG] 开始注册控件权限映射
2025-08-01 08:53:15 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 08:53:15 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 08:53:15 [INFO] 开始初始化权限验证
2025-08-01 08:53:15 [DEBUG] 设置默认UI可见性为false
2025-08-01 08:53:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:53:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 19732074
2025-08-01 08:53:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 19732074)
2025-08-01 08:53:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:53:16 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:16 [DEBUG] 开始重置 208 个命令栏
2025-08-01 08:53:16 [DEBUG] 开始检查所有需要的权限
2025-08-01 08:53:16 [INFO] 所有权限检查完成
2025-08-01 08:53:16 [DEBUG] 应用权限状态到UI控件
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:16 [DEBUG] 启动后台权限刷新任务
2025-08-01 08:53:16 [DEBUG] 启动延迟权限刷新任务
2025-08-01 08:53:16 [INFO] 权限验证初始化完成
2025-08-01 08:53:16 [INFO] UI权限管理初始化完成
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:16 [INFO] 收到权限管理器初始化完成通知
2025-08-01 08:53:16 [INFO] 开始刷新控件标题
2025-08-01 08:53:16 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:16 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:53:16 [DEBUG] 控件标题刷新完成
2025-08-01 08:53:16 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:53:16 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:53:16 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:53:16 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:16 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:53:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:53:16 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:53:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:53:16 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:53:16 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:53:16 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:53:16 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:53:16 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:53:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:53:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:53:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:53:16 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:53:16 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:53:16 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:53:16 [INFO] 控件标题更正完成
2025-08-01 08:53:16 [INFO] 控件标题刷新完成
2025-08-01 08:53:16 [INFO] 权限管理器初始化完成处理结束
2025-08-01 08:53:16 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 08:53:16 [DEBUG] 授权验证初始化完成
2025-08-01 08:53:16 [INFO] 授权状态刷新完成
2025-08-01 08:53:16 [DEBUG] 重置命令栏: cell
2025-08-01 08:53:16 [DEBUG] 重置命令栏: column
2025-08-01 08:53:16 [DEBUG] 重置命令栏: row
2025-08-01 08:53:16 [DEBUG] 重置命令栏: cell
2025-08-01 08:53:16 [DEBUG] 重置命令栏: column
2025-08-01 08:53:16 [DEBUG] 重置命令栏: row
2025-08-01 08:53:17 [DEBUG] 重置命令栏: row
2025-08-01 08:53:17 [DEBUG] 重置命令栏: column
2025-08-01 08:53:17 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-01 08:53:51 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:53:51 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:53:51 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 08:53:51 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 08:53:51 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 16653632
2025-08-01 08:53:51 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-01 08:54:08 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:08 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:08 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:08 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:08 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:08 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:08 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:09 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:09 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:09 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:09 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:10 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:10 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:10 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:11 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:11 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:11 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:11 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:12 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16256916, 新父窗口: 16064208
2025-08-01 08:54:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:12 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-01 08:54:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:54:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:12 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:12 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:12 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:12 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:12 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:13 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:13 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 16064208
2025-08-01 08:54:13 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-01 08:54:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:13 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:54:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:13 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:13 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:54:13 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:54:13 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 已存在，激活现有实例
2025-08-01 08:54:13 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:13 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:13 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:14 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:14 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:14 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:14 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:15 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:15 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:15 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:15 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:16 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:16 [DEBUG] 已重置工作表标签菜单
2025-08-01 08:54:16 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 16064208, 新父窗口: 16256916
2025-08-01 08:54:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:17 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:54:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:54:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:17 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:18 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-01 08:54:18 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:18 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:18 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:18 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16064208
2025-08-01 08:54:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16064208)
2025-08-01 08:54:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:19 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-01 08:54:19 [INFO] 系统事件监控已停止
2025-08-01 08:54:19 [INFO] Excel窗口句柄监控已停止
2025-08-01 08:54:20 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-01 08:54:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:54:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:20 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 16256916
2025-08-01 08:54:20 [INFO] 系统事件监控已启动
2025-08-01 08:54:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:20 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:54:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 16256916)
2025-08-01 08:54:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:20 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:54:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:20 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:54:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:54:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:21 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:54:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:22 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16256916
2025-08-01 08:54:22 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16256916)
2025-08-01 08:54:22 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:22 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:54:23 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-01 08:54:23 [INFO] 系统事件监控已停止
2025-08-01 08:54:23 [INFO] Excel窗口句柄监控已停止
2025-08-01 08:54:23 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-01 08:54:23 [INFO] 开始VSTO插件关闭流程
2025-08-01 08:54:23 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250801_085423.txt
2025-08-01 08:54:23 [INFO] VSTO插件关闭流程完成
2025-08-01 08:54:25 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:25 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 19732074
2025-08-01 08:54:25 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 19732074)
2025-08-01 08:54:25 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:25 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:54:32 [INFO] Excel窗口句柄监控器初始化完成
2025-08-01 08:54:32 [INFO] 配置文件实例已在加载时初始化
2025-08-01 08:54:32 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-01 08:54:32 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-01 08:54:32 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-01 08:54:32 [INFO] 成功初始化Excel应用程序实例
2025-08-01 08:54:32 [INFO] 自动备份路径未配置
2025-08-01 08:54:32 [DEBUG] 开始初始化授权控制器
2025-08-01 08:54:33 [DEBUG] 授权系统初始化完成，耗时: 317ms
2025-08-01 08:54:33 [DEBUG] 开始初始化授权验证
2025-08-01 08:54:33 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-01 08:54:33 [DEBUG] 权限管理器初始化成功
2025-08-01 08:54:33 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 08:54:33 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 08:54:33 [INFO] 开始初始化UI权限管理
2025-08-01 08:54:33 [DEBUG] [实例ID: 6bf599e9] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 08:54:33 [DEBUG] 🔍 [实例ID: 6bf599e9] 字典引用一致性检查:
2025-08-01 08:54:33 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 08:54:33 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 08:54:33 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 08:54:33 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 08:54:33 [DEBUG] 控件权限管理器初始化完成 [实例ID: 6bf599e9]
2025-08-01 08:54:33 [DEBUG] 开始注册控件权限映射
2025-08-01 08:54:33 [INFO] 开始初始化全局控件映射
2025-08-01 08:54:33 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-01 08:54:33 [DEBUG] 开始生成控件标题映射
2025-08-01 08:54:33 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 08:54:33 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 08:54:33 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 08:54:33 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-01 08:54:33 [INFO] 控件标题映射生成完成，共生成 108 项映射
2025-08-01 08:54:33 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:54:33 [DEBUG] 全局控件标题映射生成完成，共生成 108 项
2025-08-01 08:54:33 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-01 08:54:33 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-01 08:54:33 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-01 08:54:33 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-01 08:54:33 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-01 08:54:33 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-01 08:54:33 [INFO] === znAbout控件标题映射诊断 ===
2025-08-01 08:54:33 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-01 08:54:33 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-01 08:54:33 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-01 08:54:33 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-01 08:54:33 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-01 08:54:33 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-01 08:54:33 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-01 08:54:33 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-01 08:54:33 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-01 08:54:33 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-01 08:54:33 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-01 08:54:33 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-01 08:54:33 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-01 08:54:33 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-01 08:54:33 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-01 08:54:33 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-01 08:54:33 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-01 08:54:33 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-01 08:54:33 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-01 08:54:33 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-01 08:54:33 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-01 08:54:33 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-01 08:54:33 [DEBUG] 控件映射: button3 -> '关于'
2025-08-01 08:54:33 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-01 08:54:33 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-01 08:54:33 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-01 08:54:33 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-01 08:54:33 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-01 08:54:33 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-01 08:54:33 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-01 08:54:33 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-01 08:54:33 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-01 08:54:33 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-01 08:54:33 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-01 08:54:33 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-01 08:54:33 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-01 08:54:33 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-01 08:54:33 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-01 08:54:33 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-01 08:54:33 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-01 08:54:33 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-01 08:54:33 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-01 08:54:33 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-01 08:54:33 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-01 08:54:33 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-01 08:54:33 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-01 08:54:33 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-01 08:54:33 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-01 08:54:33 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-01 08:54:33 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-01 08:54:33 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-01 08:54:33 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-01 08:54:33 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-01 08:54:33 [DEBUG] 控件映射: group1 -> '关于'
2025-08-01 08:54:33 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-01 08:54:33 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-01 08:54:33 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-01 08:54:33 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-01 08:54:33 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-01 08:54:33 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-01 08:54:33 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-01 08:54:33 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-01 08:54:33 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-01 08:54:33 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-01 08:54:33 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-01 08:54:33 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-01 08:54:33 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-01 08:54:33 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-01 08:54:33 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-01 08:54:33 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-01 08:54:33 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-01 08:54:33 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-01 08:54:33 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-01 08:54:33 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-01 08:54:33 [DEBUG] 开始生成控件权限映射
2025-08-01 08:54:33 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-01 08:54:33 [DEBUG] 通过反射获取到 123 个字段
2025-08-01 08:54:33 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-01 08:54:33 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-01 08:54:33 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-01 08:54:33 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-01 08:54:33 [INFO] 控件结构获取完成，共获取到 121 个控件
2025-08-01 08:54:33 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-01 08:54:33 [INFO] 控件权限映射生成完成，共生成 115 项映射
2025-08-01 08:54:33 [DEBUG] 全局控件权限映射生成完成，共生成 115 项
2025-08-01 08:54:33 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-01 08:54:33 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-01 08:54:33 [INFO] 全局控件映射初始化完成 - 标题映射: 108 项, 权限映射: 115 项
2025-08-01 08:54:33 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 08:54:33 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 08:54:33 [INFO] 开始初始化权限验证
2025-08-01 08:54:33 [DEBUG] 设置默认UI可见性为false
2025-08-01 08:54:33 [DEBUG] 开始检查所有需要的权限
2025-08-01 08:54:33 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-01 08:54:33 [INFO] 启动网络授权信息获取任务
2025-08-01 08:54:33 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-01 08:54:33 [INFO] 所有权限检查完成
2025-08-01 08:54:33 [DEBUG] 应用权限状态到UI控件
2025-08-01 08:54:33 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:33 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:33 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:33 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:33 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:33 [DEBUG] 启动后台权限刷新任务
2025-08-01 08:54:33 [DEBUG] 启动延迟权限刷新任务
2025-08-01 08:54:33 [INFO] 权限验证初始化完成
2025-08-01 08:54:33 [INFO] UI权限管理初始化完成
2025-08-01 08:54:33 [INFO] 收到权限管理器初始化完成通知
2025-08-01 08:54:33 [INFO] 开始刷新控件标题
2025-08-01 08:54:33 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:54:33 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:54:33 [DEBUG] 控件标题刷新完成
2025-08-01 08:54:33 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:54:33 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:54:33 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:54:33 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:54:33 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:54:33 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:54:33 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:33 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:54:33 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:33 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:54:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:54:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:54:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:54:33 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:54:33 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:54:33 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:54:33 [INFO] 控件标题更正完成
2025-08-01 08:54:33 [INFO] 控件标题刷新完成
2025-08-01 08:54:33 [INFO] 权限管理器初始化完成处理结束
2025-08-01 08:54:33 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 08:54:33 [DEBUG] 授权验证初始化完成
2025-08-01 08:54:33 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-01 08:54:33 [INFO] 成功加载配置和授权信息
2025-08-01 08:54:33 [INFO] 开始初始化定时器和设置
2025-08-01 08:54:33 [INFO] 定时器和设置初始化完成
2025-08-01 08:54:33 [INFO] 开始VSTO插件启动流程
2025-08-01 08:54:34 [INFO] TopMostForm窗体加载完成
2025-08-01 08:54:34 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22548136
2025-08-01 08:54:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22548136)
2025-08-01 08:54:34 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 22548136
2025-08-01 08:54:34 [INFO] 系统事件监控已启动
2025-08-01 08:54:34 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:34 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-01 08:54:34 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-01 08:54:34 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-01 08:54:34 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-01 08:54:34 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 6366238
2025-08-01 08:54:34 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-01 08:54:34 [INFO] VSTO插件启动流程完成
2025-08-01 08:54:34 [INFO] 从Remote成功获取到网络授权信息
2025-08-01 08:54:34 [INFO] 网络授权信息已更新并触发回调
2025-08-01 08:54:34 [INFO] 网络授权信息已从 Network 更新
2025-08-01 08:54:34 [INFO] 授权版本: 1.0
2025-08-01 08:54:34 [INFO] 颁发者: ExtensionsTools
2025-08-01 08:54:34 [INFO] 用户数量: 3
2025-08-01 08:54:34 [INFO] 分组权限数量: 2
2025-08-01 08:54:34 [WARN] 配置文件中未找到用户组信息
2025-08-01 08:54:34 [INFO] 已重新设置用户组: []
2025-08-01 08:54:34 [INFO] 用户组信息已重新设置
2025-08-01 08:54:34 [INFO] 立即刷新权限缓存和UI界面
2025-08-01 08:54:34 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 08:54:34 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-01 08:54:34 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-01 08:54:34 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-01 08:54:34 [DEBUG] 本地权限缓存已清空
2025-08-01 08:54:34 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-01 08:54:34 [INFO] 所有权限检查完成
2025-08-01 08:54:34 [DEBUG] 权限重新检查完成
2025-08-01 08:54:34 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:34 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:34 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:34 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:34 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:34 [INFO] UI界面权限状态已更新
2025-08-01 08:54:34 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:54:34 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:54:34 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-01 08:54:34 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-01 08:54:34 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-01 08:54:34 [INFO] 开始刷新Ribbon控件标题
2025-08-01 08:54:34 [DEBUG] 权限缓存已清空，清除了 115 个缓存项
2025-08-01 08:54:34 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-01 08:54:34 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:54:34 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:54:34 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:34 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:54:34 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:54:34 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:54:34 [INFO] 控件标题更正完成
2025-08-01 08:54:34 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-01 08:54:34 [INFO] Ribbon控件标题刷新完成
2025-08-01 08:54:34 [INFO] 控件标题刷新完成
2025-08-01 08:54:34 [DEBUG] Ribbon控件标题已刷新
2025-08-01 08:54:34 [INFO] 开始刷新控件标题
2025-08-01 08:54:34 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:54:34 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:54:34 [DEBUG] 控件标题刷新完成
2025-08-01 08:54:34 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:54:34 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:54:34 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:54:34 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:34 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:54:34 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:34 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:54:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:54:34 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:54:34 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:54:34 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:54:34 [INFO] 控件标题更正完成
2025-08-01 08:54:34 [INFO] 控件标题刷新完成
2025-08-01 08:54:34 [DEBUG] Ribbon控件标题已立即刷新
2025-08-01 08:54:35 [INFO] 开始刷新授权状态
2025-08-01 08:54:35 [DEBUG] 开始初始化授权验证
2025-08-01 08:54:35 [DEBUG] 使用新的权限管理器进行初始化
2025-08-01 08:54:35 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-01 08:54:35 [INFO] 开始初始化UI权限管理
2025-08-01 08:54:35 [DEBUG] [实例ID: 47755bd7] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-01 08:54:35 [DEBUG] 🔍 [实例ID: 47755bd7] 字典引用一致性检查:
2025-08-01 08:54:35 [DEBUG] 🔍   标题映射一致性: True
2025-08-01 08:54:35 [DEBUG] 🔍   权限映射一致性: True
2025-08-01 08:54:35 [DEBUG] 🔍   信息映射一致性: True
2025-08-01 08:54:35 [DEBUG] 🔍   特殊控件一致性: True
2025-08-01 08:54:35 [DEBUG] 控件权限管理器初始化完成 [实例ID: 47755bd7]
2025-08-01 08:54:35 [DEBUG] 开始注册控件权限映射
2025-08-01 08:54:35 [DEBUG] 批量注册控件权限映射完成，成功: 115/115
2025-08-01 08:54:35 [DEBUG] HyExcel控件权限映射注册完成，共注册 115 个控件
2025-08-01 08:54:35 [INFO] 开始初始化权限验证
2025-08-01 08:54:35 [DEBUG] 设置默认UI可见性为false
2025-08-01 08:54:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22548136
2025-08-01 08:54:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22548136)
2025-08-01 08:54:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:35 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:35 [DEBUG] 开始重置 208 个命令栏
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:35 [DEBUG] 开始检查所有需要的权限
2025-08-01 08:54:35 [INFO] 所有权限检查完成
2025-08-01 08:54:35 [DEBUG] 应用权限状态到UI控件
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:35 [DEBUG] 启动后台权限刷新任务
2025-08-01 08:54:35 [DEBUG] 启动延迟权限刷新任务
2025-08-01 08:54:35 [INFO] 权限验证初始化完成
2025-08-01 08:54:35 [INFO] UI权限管理初始化完成
2025-08-01 08:54:35 [INFO] 收到权限管理器初始化完成通知
2025-08-01 08:54:35 [INFO] 开始刷新控件标题
2025-08-01 08:54:35 [DEBUG] 开始刷新所有控件权限状态
2025-08-01 08:54:35 [DEBUG] 控件权限状态刷新完成，已检查 115 个控件
2025-08-01 08:54:35 [DEBUG] 控件标题刷新完成
2025-08-01 08:54:35 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-01 08:54:35 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-01 08:54:35 [DEBUG] 🔍 HyRibbon反射获取到 123 个字段
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:35 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:35 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-01 08:54:35 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-01 08:54:35 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:35 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-01 08:54:35 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-01 08:54:35 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:35 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:35 [INFO] 动态获取到 121 个Ribbon控件引用
2025-08-01 08:54:35 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-01 08:54:35 [INFO] 开始批量更新控件标题，共 121 个控件
2025-08-01 08:54:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-01 08:54:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-01 08:54:35 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-01 08:54:35 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-01 08:54:35 [INFO] 批量更新控件标题完成，成功更新 108 个控件
2025-08-01 08:54:35 [INFO] 动态批量更新完成，共更新 121 个控件
2025-08-01 08:54:35 [INFO] 控件标题更正完成
2025-08-01 08:54:35 [INFO] 控件标题刷新完成
2025-08-01 08:54:35 [INFO] 权限管理器初始化完成处理结束
2025-08-01 08:54:35 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-01 08:54:35 [DEBUG] 授权验证初始化完成
2025-08-01 08:54:35 [INFO] 授权状态刷新完成
2025-08-01 08:54:36 [DEBUG] 重置命令栏: cell
2025-08-01 08:54:36 [DEBUG] 重置命令栏: column
2025-08-01 08:54:36 [DEBUG] 重置命令栏: row
2025-08-01 08:54:36 [DEBUG] 重置命令栏: cell
2025-08-01 08:54:36 [DEBUG] 重置命令栏: column
2025-08-01 08:54:36 [DEBUG] 重置命令栏: row
2025-08-01 08:54:36 [DEBUG] 重置命令栏: row
2025-08-01 08:54:36 [DEBUG] 重置命令栏: column
2025-08-01 08:54:37 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-01 08:54:38 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 08:54:38 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 08:54:38 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 08:54:38 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 08:54:38 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 41617038
2025-08-01 08:54:38 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-01 08:54:38 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-01 08:54:38 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-01 08:54:38 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:38 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-01 08:54:38 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-01 08:54:38 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-01 08:54:38 [DEBUG] 已应用权限状态到UI控件
2025-08-01 08:54:38 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:38 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:38 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:39 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:40 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:40 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:40 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:41 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:41 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:41 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:41 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:43 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-01 08:54:44 [WARN] 检测到Excel窗口句柄变化: 22548136 -> 13829338
2025-08-01 08:54:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22548136, 新父窗口: 13829338
2025-08-01 08:54:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:54:44 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-01 08:54:44 [DEBUG] 授权控制器已初始化
2025-08-01 08:54:44 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-01 08:54:45 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开事件触发
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:45 [INFO] App_WorkbookOpen: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 打开处理完成
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:45 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:45 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 08:54:45 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:45 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:45 [ERROR] 菜单系统初始化失败
异常详情：System.Runtime.InteropServices.COMException (0x800A01A8): 异常来自 HRESULT:0x800A01A8
   在 Microsoft.Office.Core.CommandBar.get_Controls()
   在 HyExcelVsto.Extensions.MenuManager.RebuildWorksheetContextMenu(CommandBar bar, Boolean isCellContext, Boolean addClick) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 287
   在 HyExcelVsto.Extensions.MenuManager.UpdateWorksheetContextMenus(CommandBars bars) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 222
   在 HyExcelVsto.Extensions.MenuManager.RebuildAllMenus() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 49
   在 HyExcelVsto.ThisAddIn.EnsureMenuInitialized() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 328
2025-08-01 08:54:45 [ERROR] 菜单系统启动时初始化失败
异常详情：System.Runtime.InteropServices.COMException (0x800A01A8): 异常来自 HRESULT:0x800A01A8
   在 Microsoft.Office.Core.CommandBar.get_Controls()
   在 HyExcelVsto.Extensions.MenuManager.RebuildWorksheetContextMenu(CommandBar bar, Boolean isCellContext, Boolean addClick) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 287
   在 HyExcelVsto.Extensions.MenuManager.UpdateWorksheetContextMenus(CommandBars bars) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 222
   在 HyExcelVsto.Extensions.MenuManager.RebuildAllMenus() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 49
   在 HyExcelVsto.ThisAddIn.EnsureMenuInitialized() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 336
   在 HyExcelVsto.ThisAddIn.<ThisAddIn_Startup>b__45_1() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 273
2025-08-01 08:54:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:46 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 08:54:46 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:54:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:49 [INFO] App_WorkbookOpen: 工作簿 '☆填写PPT.xlsx' 打开事件触发
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:49 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 08:54:49 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:49 [INFO] App_WorkbookOpen: 工作簿 '☆填写PPT.xlsx' 打开处理完成
2025-08-01 08:54:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:54:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:54:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:49 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:49 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:49 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:49 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:54:49 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:54:49 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:54:49 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 08:54:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:50 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:54:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 08:54:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:50 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:54:50 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:54:50 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-01 08:55:10 [INFO] OpenForm: 保持窗体现有标题 '存档及发送'
2025-08-01 08:55:10 [INFO] OpenForm: 准备打开窗体 '存档及发送'，位置: Center，单实例: True
2025-08-01 08:55:10 [INFO] 开始显示窗体 '存档及发送'，位置模式: Center
2025-08-01 08:55:10 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\Data\ET\Cache\ETForm\ComboBoxHistory\frm备份及发送_comboBox接收者.data
2025-08-01 08:55:10 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.data\FileReceiver.txt
2025-08-01 08:55:10 [INFO] 窗体 '存档及发送' 以TopMostForm为父窗体显示
2025-08-01 08:55:10 [INFO] 窗体 '存档及发送' 显示完成，句柄: 16913102
2025-08-01 08:55:10 [INFO] OpenForm: 窗体 '存档及发送' 打开成功
2025-08-01 08:58:42 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:58:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 13829338
2025-08-01 08:58:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:58:42 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:58:42 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 08:58:42 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:58:42 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 08:58:42 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:58:42 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:58:42 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:58:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 08:58:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:58:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:58:42 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:58:42 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:58:42 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 08:58:42 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:58:42 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:59:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:59:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 08:59:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 08:59:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:59:35 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 08:59:36 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:59:36 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 08:59:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:59:36 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:59:36 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 08:59:36 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 08:59:36 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 08:59:36 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 08:59:36 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 08:59:36 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:59:36 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 08:59:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:59:36 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 08:59:36 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 08:59:37 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 08:59:37 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 08:59:37 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 08:59:37 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:00:07 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:00:07 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:00:07 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:00:07 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:00:07 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 09:00:07 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:00:07 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:00:07 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:00:07 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:00:07 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:00:07 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:00:07 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:00:07 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:00:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:00:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:00:08 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:00:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:00:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:00:08 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:00:14 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:00:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 09:00:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:00:14 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:00:14 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:00:14 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:00:14 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:00:14 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:00:14 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:00:14 [WARN] 检测到Excel窗口句柄变化: 13829338 -> 22613672
2025-08-01 09:00:14 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:00:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:00:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:00:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:00:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:00:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:00:14 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:00:14 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:00:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:01:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:01:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:01:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:01:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:01:32 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 09:02:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 13829338
2025-08-01 09:02:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:10 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:02:10 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:02:10 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:10 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:10 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:12 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:12 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:12 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 09:02:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:12 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:02:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:12 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:12 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:12 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:12 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:12 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:20 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:20 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:20 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:20 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:20 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 09:02:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:02:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:20 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:02:20 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:20 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:02:20 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:20 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:21 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:21 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:21 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:21 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:21 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:23 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-01 09:02:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:23 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:02:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:23 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:24 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 22613672
2025-08-01 09:02:24 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-01 09:02:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:24 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:26 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 13829338
2025-08-01 09:02:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:26 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:26 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:02:26 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:26 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:02:26 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:26 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:26 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:27 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:27 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 09:02:27 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:27 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:27 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:02:27 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:27 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:27 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:27 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:28 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:28 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:28 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 13829338
2025-08-01 09:02:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:30 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:02:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:30 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:02:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:30 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:31 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:02:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:02:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 09:02:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:40 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:02:40 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:02:40 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:02:40 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:02:40 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:02:40 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:40 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:40 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:40 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:02:40 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:02:40 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:02:41 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:02:41 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:02:41 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:03:58 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 09:03:58 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 09:03:58 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 09:03:58 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 09:03:58 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 2433010
2025-08-01 09:03:58 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-01 09:04:04 [INFO] OpenForm: 窗体标题已设置为类名 '字符处理'
2025-08-01 09:04:04 [INFO] OpenForm: 准备打开窗体 '字符处理'，位置: Center，单实例: True
2025-08-01 09:04:04 [INFO] 开始显示窗体 '字符处理'，位置模式: Center
2025-08-01 09:04:04 [INFO] [ET.ETForm] 成功加载配置文件菜单：字符规整预置.config，共 5 个配置项
2025-08-01 09:04:04 [INFO] [ET.ETSectionConfigReader] 配置文件加载完成: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\正则表达式预置.config, 共 12 个配置节, 48 个键值对
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：获取基站名 - [\\u4E00-\\u9FFF]+
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取 - | _ 前内容 - [^-_\\|]*
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取 . 前内容 - ^(.*?)(?=\\.|$)
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取 800 前内容 - ^(.+?)-800.*$
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取数字 - \\d+
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取邮箱地址 - [a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取手机号码 - 1[3-9]\\d{9}
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取IP地址 - \\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取日期(YYYY-MM-DD) - \\d{4}-\\d{2}-\\d{2}
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取时间(HH:MM:SS) - \\d{2}:\\d{2}:\\d{2}
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取括号内容 - \\(([^)]+)\\)
2025-08-01 09:04:04 [DEBUG] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载正则表达式规则：提取方括号内容 - \\[([^\\]]+)\\]
2025-08-01 09:04:04 [INFO] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载 12 个正则表达式规则
2025-08-01 09:04:04 [INFO] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 成功加载 12 个正则表达式规则到ListView
2025-08-01 09:04:04 [INFO] 窗体 '字符处理' 以TopMostForm为父窗体显示
2025-08-01 09:04:04 [INFO] 窗体 '字符处理' 显示完成，句柄: 11211854
2025-08-01 09:04:04 [INFO] OpenForm: 窗体 '字符处理' 打开成功
2025-08-01 09:04:08 [INFO] [HyExcelVsto.Module.Common.frm字符处理, Text: 字符处理] 应用正则表达式规则：提取 - | _ 前内容 - [^-_\\|]*
2025-08-01 09:04:22 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:04:22 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 13829338
2025-08-01 09:04:22 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:04:22 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:04:22 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:04:22 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:04:22 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:04:22 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:04:22 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:04:22 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:04:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:04:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:04:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:04:23 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:04:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:04:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:04:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:04:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:04:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:04:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:04:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:05:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:05:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 09:05:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:05:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:05:17 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:05:17 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:05:17 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:05:17 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:05:17 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:05:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:05:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:05:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:05:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:05:17 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:05:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:05:18 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:05:18 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:05:18 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:06:31 [INFO] App_WorkbookOpen: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 打开事件触发
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:06:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 10292406
2025-08-01 09:06:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:06:31 [INFO] App_WorkbookOpen: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 打开处理完成
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:06:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:06:31 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:06:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:06:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:06:31 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:06:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:06:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:06:32 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:06:32 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:06:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:06:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:06:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:06:32 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-01 09:09:46 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:09:46 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10292406, 新父窗口: 22613672
2025-08-01 09:09:46 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:09:46 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:09:46 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:09:46 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:09:46 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:09:46 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:09:46 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:09:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:09:46 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:09:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:09:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:09:47 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:09:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:09:47 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:09:47 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:09:47 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:09:47 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 10292406
2025-08-01 09:09:47 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:09:47 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:09:47 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-08-01 09:09:47 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:09:47 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:09:47 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:09:47 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:09:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:09:48 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:09:48 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:09:48 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:09:48 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:09:48 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:09:48 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:09:48 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10292406, 新父窗口: 13829338
2025-08-01 09:10:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:10:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:15 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-08-01 09:10:15 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:15 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13829338)
2025-08-01 09:10:15 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:15 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:10:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:10:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:10:15 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:15 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:10:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13829338
2025-08-01 09:10:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13829338)
2025-08-01 09:10:15 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13829338, 新父窗口: 22613672
2025-08-01 09:10:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:16 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:10:16 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:16 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:10:16 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:16 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:10:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:10:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:17 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:10:17 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:17 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:10:17 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:17 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 10292406
2025-08-01 09:10:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:24 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-08-01 09:10:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:10:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:24 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:10:24 [WARN] 检测到Excel窗口句柄变化: 22613672 -> 10292406
2025-08-01 09:10:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:10:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:10:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:24 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:10:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:10:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10292406, 新父窗口: 22613672
2025-08-01 09:10:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:35 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:10:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:35 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:10:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:35 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:10:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:10:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:35 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:10:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:10:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:10:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 10292406
2025-08-01 09:10:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:56 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-08-01 09:10:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:10:56 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:10:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:10:56 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:10:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:10:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:10:56 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:10:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:10:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:10:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:10:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:11:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:11:05 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 10292406, 新父窗口: 22613672
2025-08-01 09:11:05 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:11:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:11:05 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-08-01 09:11:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:11:05 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 22613672)
2025-08-01 09:11:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:11:05 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:11:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:11:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:11:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:11:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:11:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:11:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:11:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 22613672
2025-08-01 09:11:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 22613672)
2025-08-01 09:11:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:11:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:11:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 22613672, 新父窗口: 10292406
2025-08-01 09:11:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:11:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:11:24 [INFO] App_WorkbookActivate: 工作簿 '提需求-【3.5铁塔初步评审】揭阳0305(已经按这个购买天线，已考虑预留)-20250711.xlsx' 激活处理完成
2025-08-01 09:11:24 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-01 09:11:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:11:24 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-01 09:11:24 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-01 09:11:24 [WARN] 检测到Excel窗口句柄变化: 22613672 -> 10292406
2025-08-01 09:11:24 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 10292406)
2025-08-01 09:11:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:11:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:11:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:11:24 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-01 09:11:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:11:24 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-01 09:11:24 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 10292406
2025-08-01 09:11:24 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 10292406)
2025-08-01 09:11:24 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-01 09:11:31 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-01 09:11:31 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-01 09:11:31 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-01 09:11:31 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-01 09:11:31 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 36117906
2025-08-01 09:11:31 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
