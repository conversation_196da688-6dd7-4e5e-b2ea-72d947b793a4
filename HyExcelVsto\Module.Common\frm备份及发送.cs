using Common.Utility;
using ET;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// Excel文件备份及发送窗体
    /// </summary>
    /// <remarks>
    /// 此窗体提供以下功能：
    /// 1. 生成发送存档：创建带有日期、时间、接收者信息的文件副本
    /// 2. 存档备份：创建带有时间戳和说明的备份文件
    /// 3. 临时文件存档：创建临时文件夹中的文件副本，用于临时存储
    /// 4. 文件路径管理：复制、打开目录等操作
    /// </remarks>
    public partial class frm备份及发送 : Form
    {
        /// <summary>
        /// 初始化窗体
        /// </summary>
        public frm备份及发送()
        {
            InitializeComponent();
            Text = "存档及发送";
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void frmBackAndSend_Load(object sender, EventArgs e)
        {
            try
            {
                // 绑定配置项到控件
                ETForm.BindWindowsFormControl(textBox存档路径, ThisAddIn.ConfigurationSettings, "file", "backupfolder");
                ETForm.BindWindowsFormControl(textBox临时文件主目录, ThisAddIn.ConfigurationSettings, "file", "tempfolder");
                ETForm.BindComboBox(comboBox接收者, 30, false, ETConfig.GetConfigDirectory("FileReceiver.txt", ".data"));

                // 初始化文件接收者配置编辑器
                InitializeFileReceiverConfigEditor();

                // 设置默认临时文件主目录（如果配置为空）
                if (string.IsNullOrEmpty(textBox临时文件主目录.Text))
                {
                    textBox临时文件主目录.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "临时文件");
                    ThisAddIn.ConfigurationSettings.SetValue("file", "tempfolder", textBox临时文件主目录.Text);
                }

                // 获取当前活动工作簿并设置文件名
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = GetActiveWorkbook();
                txt发送文件名.Text = FileOperate.GetFileNameNoExtension(activeWorkbook.Name);

                // 初始化临时文件标签页显示
                InitializeTempFileDisplay(activeWorkbook);

                // 为所有路径文本框设置右键菜单
                SetupContextMenuForPathTextBoxes();
            }
            catch (Exception ex)
            {
                throw new ETException("窗体加载失败", "界面操作", ex);
            }
        }

        /// <summary>
        /// 生成发送存档按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button生成发送存档_Click(object sender, EventArgs e)
        {
            try
            {
                string outputPath = 生成发送存档(
                    fileName: txt发送文件名.Text,
                    includeDate: chk日期.Checked,
                    includeTime: chk时间.Checked,
                    recipient: comboBox接收者.Text,
                    description: textBox说明.Text
                );

                textBox输出路径.Text = outputPath;
                ETFile.FileCopyToClipboard(outputPath);
                autoResetLabel提示1.Text = "已复制文件路径到剪贴板";
                ETLogManager.Info($"成功生成发送存档：{outputPath}");
            }
            catch (Exception ex)
            {
                throw new ETException("生成发送存档失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 存档备份按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button存档备份_Click(object sender, EventArgs e)
        {
            try
            {
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = GetActiveWorkbook();

                string outputPath = 生成发送存档(
                    fileName: FileOperate.GetFileNameNoExtension(activeWorkbook.Name),
                    includeDate: true,
                    includeTime: true,
                    recipient: string.Empty,
                    description: textBox加备注.Text,
                    backupFolder: "手动备份存档"
                );

                textBox备份存档路径.Text = outputPath;
                ETFile.FileCopyToClipboard(outputPath);
                autoResetLabel提示2.Text = "已复制文件路径到剪贴板";
                ETLogManager.Info($"成功生成备份存档：{outputPath}");
            }
            catch (Exception ex)
            {
                throw new ETException("存档备份失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 另存到临时文件夹按钮点击事件处理 功能：另存副本到临时文件夹，当前操作的文件保持不变
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button另存到临时文件夹_Click(object sender, EventArgs e)
        {
            try
            {
                string outputPath = 生成临时文件存档(false); // false = 另存副本，当前文件不变
                ETFile.FileCopyToClipboard(outputPath);
                // autoResetLabel提示3 的更新已在 生成临时文件存档 方法中处理
                ETLogManager.Info($"成功另存副本到临时文件夹：{outputPath}");
            }
            catch (Exception ex)
            {
                autoResetLabel提示3.Text = "另存到临时文件夹失败";
                throw new ETException("另存到临时文件夹失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 保存到临时文件夹按钮点击事件处理 功能：保存到临时文件夹，当前操作的文件变为临时文件夹中的文件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button保存到临时文件夹_Click(object sender, EventArgs e)
        {
            try
            {
                string outputPath = 生成临时文件存档(true); // true = 保存到临时文件夹，当前文件变为临时文件
                ETFile.FileCopyToClipboard(outputPath);
                // autoResetLabel提示3 的更新已在 生成临时文件存档 方法中处理
                ETLogManager.Info($"成功保存到临时文件夹：{outputPath}");
            }
            catch (Exception ex)
            {
                autoResetLabel提示3.Text = "保存到临时文件夹失败";
                throw new ETException("保存到临时文件夹失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 获取当前活动工作簿
        /// </summary>
        /// <returns>当前活动工作簿</returns>
        /// <exception cref="ETException">当没有打开的工作簿时抛出</exception>
        private Microsoft.Office.Interop.Excel.Workbook GetActiveWorkbook()
        {
            Microsoft.Office.Interop.Excel.Workbook activeWorkbook = ThisAddIn.ExcelApplication.ActiveWorkbook;
            if (activeWorkbook == null)
            {
                throw new ETException("没有打开的工作簿", "工作簿操作");
            }
            return activeWorkbook;
        }

        /// <summary>
        /// 初始化临时文件标签页显示
        /// </summary>
        /// <param name="activeWorkbook">当前活动工作簿</param>
        private void InitializeTempFileDisplay(Microsoft.Office.Interop.Excel.Workbook activeWorkbook)
        {
            try
            {
                // 显示当前文件名
                autoResetLabel提示3.Text = $"当前文件：{activeWorkbook.Name}";

                // 计算并显示目标临时文件路径
                string targetPath = GetTempFilePath(activeWorkbook);
                textBox临时文件路径.Text = targetPath;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化临时文件显示失败", ex);
                autoResetLabel提示3.Text = "初始化失败";
                textBox临时文件路径.Text = "";
            }
        }

        /// <summary>
        /// 获取临时文件的目标路径
        /// </summary>
        /// <param name="activeWorkbook">当前活动工作簿</param>
        /// <returns>临时文件的完整路径</returns>
        private string GetTempFilePath(Microsoft.Office.Interop.Excel.Workbook activeWorkbook)
        {
            // 获取临时文件主目录
            string tempMainDir = textBox临时文件主目录.Text;
            if (string.IsNullOrEmpty(tempMainDir))
            {
                tempMainDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "临时文件");
            }

            // 创建日期子目录：主目录/{yyyy-MM-dd}
            string dateFolder = DateTime.Now.ToString("yyyy-MM-dd");
            string targetDir = Path.Combine(tempMainDir, dateFolder);

            // 获取原文件信息
            string originalFileName = Path.GetFileNameWithoutExtension(activeWorkbook.Name);
            string originalExtension = Path.GetExtension(activeWorkbook.Name);

            // 生成目标文件名：直接使用原文件名
            string newFileName = $"{originalFileName}{originalExtension}";
            return Path.Combine(targetDir, newFileName);
        }

        /// <summary>
        /// 初始化文件接收者配置编辑器
        /// </summary>
        private void InitializeFileReceiverConfigEditor()
        {
            try
            {
                // 获取FileReceiver.txt文件路径
                string fileReceiverPath = ETConfig.GetConfigDirectory("FileReceiver.txt", ".data");

                // 读取文件内容并显示在TextBox中
                if (File.Exists(fileReceiverPath))
                {
                    string content = File.ReadAllText(fileReceiverPath, Encoding.UTF8);
                    textBox文件接收者配置.Text = content;
                }
                else
                {
                    // 如果文件不存在，创建默认内容
                    textBox文件接收者配置.Text = "// 文件接收者配置\r\n// 每行一个接收者名称\r\n";
                }

                // 绑定TextChanged事件实现实时保存
                textBox文件接收者配置.TextChanged += TextBox文件接收者配置_TextChanged;

                ETLogManager.Info($"文件接收者配置编辑器初始化完成，配置文件路径：{fileReceiverPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化文件接收者配置编辑器失败", ex);
                textBox文件接收者配置.Text = "// 初始化失败，请检查日志";
            }
        }

        /// <summary>
        /// 文件接收者配置TextBox内容变更事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void TextBox文件接收者配置_TextChanged(object sender, EventArgs e)
        {
            try
            {
                // 获取FileReceiver.txt文件路径
                string fileReceiverPath = ETConfig.GetConfigDirectory("FileReceiver.txt", ".data");

                // 确保目录存在
                string directory = Path.GetDirectoryName(fileReceiverPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 实时保存内容到文件
                File.WriteAllText(fileReceiverPath, textBox文件接收者配置.Text, Encoding.UTF8);

                ETLogManager.Debug($"文件接收者配置已保存到：{fileReceiverPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("保存文件接收者配置失败", ex);
            }
        }

        /// <summary>
        /// 设置路径文本框的右键菜单
        /// </summary>
        private void SetupContextMenuForPathTextBoxes()
        {
            // 为所有路径文本框设置相同的右键菜单
            textBox输出路径.ContextMenuStrip = contextMenuStrip1;
            textBox备份存档路径.ContextMenuStrip = contextMenuStrip1;
            textBox临时文件路径.ContextMenuStrip = contextMenuStrip1;
        }

        /// <summary>
        /// 生成临时文件存档
        /// </summary>
        /// <param name="saveToTemp">true=保存到临时文件夹(当前文件变为临时文件), false=另存副本到临时文件夹(当前文件不变)</param>
        /// <returns>生成的文件完整路径</returns>
        private string 生成临时文件存档(bool saveToTemp)
        {
            try
            {
                Microsoft.Office.Interop.Excel.Workbook activeWorkbook = GetActiveWorkbook();

                // 使用已经计算好的目标路径
                string filePath = textBox临时文件路径.Text;
                if (string.IsNullOrEmpty(filePath))
                {
                    // 如果路径为空，重新计算
                    filePath = GetTempFilePath(activeWorkbook);
                }

                // 确保目标目录存在
                string targetDir = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                if (saveToTemp)
                {
                    // 功能1：保存到临时文件夹 - 当前文件变为临时文件夹中的文件
                    activeWorkbook.SaveAs(filePath);
                    ETLogManager.Info($"文件已保存到临时文件夹，当前操作文件变为：{filePath}");

                    // 更新显示当前文件名
                    autoResetLabel提示3.Text = $"当前文件：{Path.GetFileName(filePath)}";
                }
                else
                {
                    // 功能2：另存副本到临时文件夹 - 当前文件保持不变
                    activeWorkbook.SaveCopyAs(filePath);
                    ETLogManager.Info($"文件副本已保存到临时文件夹：{filePath}，当前操作文件保持不变");

                    // 保持显示原文件名
                    autoResetLabel提示3.Text = $"当前文件：{activeWorkbook.Name}";
                }

                // 生成备注文件
                if (!string.IsNullOrWhiteSpace(textBox临时文件备注.Text))
                {
                    string originalFileName = Path.GetFileNameWithoutExtension(activeWorkbook.Name);
                    string noteFileName = $"{originalFileName}.note";
                    string noteFilePath = Path.Combine(targetDir, noteFileName);
                    File.WriteAllText(noteFilePath, textBox临时文件备注.Text, Encoding.UTF8);
                    File.SetAttributes(noteFilePath, FileAttributes.ReadOnly);
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new ETException("生成临时文件存档失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 生成发送存档文件
        /// </summary>
        /// <param name="fileName">文件名（不含扩展名）</param>
        /// <param name="includeDate">是否包含日期</param>
        /// <param name="includeTime">是否包含时间</param>
        /// <param name="recipient">接收者</param>
        /// <param name="description">说明文本</param>
        /// <param name="backupFolder">备份文件夹名称，默认为"发送存档"</param>
        /// <returns>生成的文件完整路径</returns>
        /// <exception cref="HyException">当文件操作失败时抛出</exception>
        private string 生成发送存档(string fileName, bool includeDate, bool includeTime,
            string recipient, string description, string backupFolder = "发送存档")
        {
            try
            {
                // 生成文件名
                string sanitizedFileName = fileName.Trim().PathRemoveInvalidChars("_");
                string baseFileName = string.IsNullOrEmpty(sanitizedFileName)
                    ? ThisAddIn.ExcelApplication.ActiveWorkbook.Name
                    : sanitizedFileName;

                // 获取原始工作簿的扩展名和文件名
                string originalFile = ThisAddIn.ExcelApplication.ActiveWorkbook.Name;
                string fileExtension = originalFile.Contains(".")
                    ? originalFile.Substring(originalFile.LastIndexOf('.'))
                    : ".xlsx";

                // 如果用户输入的文件名包含扩展名，则移除
                string fileNameWithoutExt = baseFileName;
                if (baseFileName.EndsWith(fileExtension, StringComparison.OrdinalIgnoreCase))
                {
                    fileNameWithoutExt = baseFileName.Substring(0, baseFileName.Length - fileExtension.Length);
                }

                // 构建文件名部分
                List<string> fileNameParts = [fileNameWithoutExt];
                DateTime now = DateTime.Now;
                if (includeDate) fileNameParts.Add(now.ToString("yyyyMMdd"));
                if (includeTime) fileNameParts.Add(now.ToString("HHmmss"));

                // 创建目标路径
                string basePath = Path.Combine(GlobalSettings.AutoBackupPath, backupFolder, now.ToString("yyyy年"));
                if (!string.IsNullOrWhiteSpace(recipient))
                {
                    basePath = Path.Combine(basePath, recipient);
                }

                string subDir = Path.Combine(basePath, $"{now:yyyyMMdd-HHmmss}-{fileNameWithoutExt}");
                Directory.CreateDirectory(subDir);

                string fullFileName = $"{string.Join("-", fileNameParts)}{fileExtension}";
                string filePath = Path.Combine(subDir, fullFileName);

                // 保存副本到指定路径
                ETExcelExtensions.SaveCopyAs(ThisAddIn.ExcelApplication.ActiveWorkbook, filePath);

                // 将文件添加至锁定文件管理，并设置为只读
                ThisAddIn.AddToLockFile(filePath);
                File.SetAttributes(filePath, File.GetAttributes(filePath) | FileAttributes.ReadOnly);

                // 生成说明文件或备注文件
                if (!string.IsNullOrWhiteSpace(description))
                {
                    string noteExtension = backupFolder == "手动备份存档" ? ".note" : "_说明.txt";
                    string noteFileName = backupFolder == "手动备份存档"
                        ? $"{Path.GetFileNameWithoutExtension(fullFileName)}.note"
                        : $"{Path.GetFileNameWithoutExtension(fullFileName)}_说明.txt";
                    string noteFilePath = Path.Combine(subDir, noteFileName);
                    File.WriteAllText(noteFilePath, description, Encoding.UTF8);
                    File.SetAttributes(noteFilePath, FileAttributes.ReadOnly);
                }

                return filePath;
            }
            catch (Exception ex)
            {
                throw new ETException("生成存档文件失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 输出路径双击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void textBox输出路径_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                string filePath = ((TextBox)sender).Text.Trim();
                if (File.Exists(filePath))
                {
                    ETFile.FileCopyToClipboard(filePath);
                    ETLogManager.Info($"已复制文件路径到剪贴板：{filePath}");
                }
                else
                {
                    MessageBox.Show(@"文件不存在");
                    ETLogManager.Error("尝试复制不存在的文件路径", new FileNotFoundException($"文件不存在：{filePath}"));
                }
            }
            catch (Exception ex)
            {
                throw new ETException("复制文件路径失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 获取触发右键菜单的源文本框
        /// </summary>
        /// <param name="sender">菜单项发送者</param>
        /// <returns>对应的文本框控件</returns>
        private TextBox GetSourceTextBox(object sender)
        {
            try
            {
                // 从菜单项获取ContextMenuStrip
                if (sender is ToolStripMenuItem menuItem && menuItem.Owner is ContextMenuStrip contextMenu)
                {
                    // 获取触发右键菜单的控件
                    Control sourceControl = contextMenu.SourceControl;

                    // 直接返回文本框控件
                    if (sourceControl is TextBox textBox)
                    {
                        return textBox;
                    }
                }

                // 如果无法确定源控件，默认返回生成标签页的文本框
                return textBox输出路径;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取源文本框失败", ex);
                return textBox输出路径; // 默认返回生成标签页的文本框
            }
        }

        /// <summary>
        /// 复制路径菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 复制路径ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 获取触发右键菜单的文本框
            TextBox sourceTextBox = GetSourceTextBox(sender);
            if (sourceTextBox == null || string.IsNullOrEmpty(sourceTextBox.Text)) return;

            Clipboard.SetText(sourceTextBox.Text);
            ETLogManager.Info($"已复制路径到剪贴板：{sourceTextBox.Text}");
        }

        /// <summary>
        /// 打开所在目录菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 打开所在目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取触发右键菜单的文本框
                TextBox sourceTextBox = GetSourceTextBox(sender);
                if (sourceTextBox == null || string.IsNullOrEmpty(sourceTextBox.Text)) return;

                FileInfo fileInfo = new(sourceTextBox.Text);
                if (fileInfo.DirectoryName != null)
                {
                    Process.Start(fileInfo.DirectoryName);
                    ETLogManager.Info($"已打开目录：{fileInfo.DirectoryName}");
                }
            }
            catch (Exception ex)
            {
                throw new ETException("打开目录失败", "文件操作", ex);
            }
        }

        /// <summary>
        /// 复制文件菜单项点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void 复制文件ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 获取触发右键菜单的文本框
            TextBox sourceTextBox = GetSourceTextBox(sender);
            if (sourceTextBox == null || string.IsNullOrEmpty(sourceTextBox.Text)) return;

            string filePath = sourceTextBox.Text;
            if (File.Exists(filePath))
            {
                ETFile.FileCopyToClipboard(filePath);
                ETLogManager.Info($"已复制文件到剪贴板：{filePath}");
            }
            else
            {
                MessageBox.Show(@"文件不存在");
                ETLogManager.Error("尝试复制不存在的文件", new FileNotFoundException($"文件不存在：{filePath}"));
            }
        }
    }
}