using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ET;

namespace HyAssistant
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            this.contextMenuStripNotifyIconMain = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.网页常挂助手ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.网页常挂助手V2ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件复制助手ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件解压助手ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.邮箱附件接收助手ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.监控Visio文件自动转PDFToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.监控文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.中国铁塔照片下载助手ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.新建目录ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
            this.授权管理ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.关于ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripSeparator();
            this.ExitToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.notifyIconMain = new System.Windows.Forms.NotifyIcon(this.components);
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPageText = new System.Windows.Forms.TabPage();
            this.textBox文本记录 = new System.Windows.Forms.TextBox();
            this.tabPageText2 = new System.Windows.Forms.TabPage();
            this.textBox文本记录2 = new System.Windows.Forms.TextBox();
            this.tabPageText3 = new System.Windows.Forms.TabPage();
            this.textBox文本记录3 = new System.Windows.Forms.TextBox();
            this.tabPageText4 = new System.Windows.Forms.TabPage();
            this.textBox文本记录4 = new System.Windows.Forms.TextBox();
            this.tabPageText5 = new System.Windows.Forms.TabPage();
            this.textBox文本记录5 = new System.Windows.Forms.TextBox();
            this.tabPageLog = new System.Windows.Forms.TabPage();
            this.contextMenuStripNotifyIconMain.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPageText.SuspendLayout();
            this.tabPageText2.SuspendLayout();
            this.tabPageText3.SuspendLayout();
            this.tabPageText4.SuspendLayout();
            this.tabPageText5.SuspendLayout();
            this.tabPageLog.SuspendLayout();
            this.SuspendLayout();
            // 
            // contextMenuStripNotifyIconMain
            // 
            this.contextMenuStripNotifyIconMain.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.网页常挂助手ToolStripMenuItem,
            this.网页常挂助手V2ToolStripMenuItem,
            this.文件复制助手ToolStripMenuItem,
            this.文件解压助手ToolStripMenuItem,
            this.邮箱附件接收助手ToolStripMenuItem,
            this.监控Visio文件自动转PDFToolStripMenuItem,
            this.监控文件ToolStripMenuItem,
            this.中国铁塔照片下载助手ToolStripMenuItem,
            this.新建目录ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.授权管理ToolStripMenuItem,
            this.关于ToolStripMenuItem,
            this.toolStripMenuItem1,
            this.ExitToolStripMenuItem});
            this.contextMenuStripNotifyIconMain.Name = "contextMenuStrip2";
            this.contextMenuStripNotifyIconMain.Size = new System.Drawing.Size(181, 302);
            // 
            // 网页常挂助手ToolStripMenuItem
            // 
            this.网页常挂助手ToolStripMenuItem.Name = "网页常挂助手ToolStripMenuItem";
            this.网页常挂助手ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.网页常挂助手ToolStripMenuItem.Text = "网页常挂助手";
            this.网页常挂助手ToolStripMenuItem.Visible = false;
            this.网页常挂助手ToolStripMenuItem.Click += new System.EventHandler(this.网页常挂助手ToolStripMenuItem_Click);
            // 
            // 网页常挂助手V2ToolStripMenuItem
            // 
            this.网页常挂助手V2ToolStripMenuItem.Name = "网页常挂助手V2ToolStripMenuItem";
            this.网页常挂助手V2ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.网页常挂助手V2ToolStripMenuItem.Text = "网页常挂助手V2";
            this.网页常挂助手V2ToolStripMenuItem.Visible = false;
            this.网页常挂助手V2ToolStripMenuItem.Click += new System.EventHandler(this.网页常挂助手V2ToolStripMenuItem_Click);
            // 
            // 文件复制助手ToolStripMenuItem
            // 
            this.文件复制助手ToolStripMenuItem.Name = "文件复制助手ToolStripMenuItem";
            this.文件复制助手ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.文件复制助手ToolStripMenuItem.Text = "文件复制助手";
            this.文件复制助手ToolStripMenuItem.Visible = false;
            this.文件复制助手ToolStripMenuItem.Click += new System.EventHandler(this.文件复制助手ToolStripMenuItem_Click);
            // 
            // 文件解压助手ToolStripMenuItem
            // 
            this.文件解压助手ToolStripMenuItem.Name = "文件解压助手ToolStripMenuItem";
            this.文件解压助手ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.文件解压助手ToolStripMenuItem.Text = "文件解压助手";
            this.文件解压助手ToolStripMenuItem.Visible = false;
            this.文件解压助手ToolStripMenuItem.Click += new System.EventHandler(this.文件解压助手ToolStripMenuItem_Click);
            // 
            // 邮箱附件接收助手ToolStripMenuItem
            // 
            this.邮箱附件接收助手ToolStripMenuItem.Name = "邮箱附件接收助手ToolStripMenuItem";
            this.邮箱附件接收助手ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.邮箱附件接收助手ToolStripMenuItem.Text = "邮箱附件接收助手";
            this.邮箱附件接收助手ToolStripMenuItem.Visible = false;
            this.邮箱附件接收助手ToolStripMenuItem.Click += new System.EventHandler(this.邮箱附件接收助手ToolStripMenuItem_Click);
            // 
            // 监控Visio文件自动转PDFToolStripMenuItem
            // 
            this.监控Visio文件自动转PDFToolStripMenuItem.Name = "监控Visio文件自动转PDFToolStripMenuItem";
            this.监控Visio文件自动转PDFToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.监控Visio文件自动转PDFToolStripMenuItem.Text = "Visio自动转PDF";
            this.监控Visio文件自动转PDFToolStripMenuItem.Visible = false;
            this.监控Visio文件自动转PDFToolStripMenuItem.Click += new System.EventHandler(this.监控Visio文件自动转PDFToolStripMenuItem_Click);
            // 
            // 监控文件ToolStripMenuItem
            // 
            this.监控文件ToolStripMenuItem.Name = "监控文件ToolStripMenuItem";
            this.监控文件ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.监控文件ToolStripMenuItem.Text = "监控文件";
            this.监控文件ToolStripMenuItem.Visible = false;
            this.监控文件ToolStripMenuItem.Click += new System.EventHandler(this.监控文件ToolStripMenuItem_Click);
            // 
            // 中国铁塔照片下载助手ToolStripMenuItem
            // 
            this.中国铁塔照片下载助手ToolStripMenuItem.Name = "中国铁塔照片下载助手ToolStripMenuItem";
            this.中国铁塔照片下载助手ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.中国铁塔照片下载助手ToolStripMenuItem.Text = "照片下载助手";
            this.中国铁塔照片下载助手ToolStripMenuItem.Visible = false;
            this.中国铁塔照片下载助手ToolStripMenuItem.Click += new System.EventHandler(this.中国铁塔照片下载助手ToolStripMenuItem_Click);
            // 
            // 新建目录ToolStripMenuItem
            // 
            this.新建目录ToolStripMenuItem.Name = "新建目录ToolStripMenuItem";
            this.新建目录ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.新建目录ToolStripMenuItem.Text = "新建目录";
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(177, 6);
            // 
            // 授权管理ToolStripMenuItem
            // 
            this.授权管理ToolStripMenuItem.Name = "授权管理ToolStripMenuItem";
            this.授权管理ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.授权管理ToolStripMenuItem.Text = "授权管理";
            this.授权管理ToolStripMenuItem.Visible = false;
            this.授权管理ToolStripMenuItem.Click += new System.EventHandler(this.授权管理ToolStripMenuItem_Click);
            // 
            // 关于ToolStripMenuItem
            // 
            this.关于ToolStripMenuItem.Name = "关于ToolStripMenuItem";
            this.关于ToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.关于ToolStripMenuItem.Text = "关于";
            this.关于ToolStripMenuItem.Click += new System.EventHandler(this.关于ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(177, 6);
            // 
            // ExitToolStripMenuItem
            // 
            this.ExitToolStripMenuItem.Name = "ExitToolStripMenuItem";
            this.ExitToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.ExitToolStripMenuItem.Text = "退出";
            this.ExitToolStripMenuItem.Click += new System.EventHandler(this.ExitButton_Click);
            // 
            // notifyIconMain
            // 
            this.notifyIconMain.ContextMenuStrip = this.contextMenuStripNotifyIconMain;
            this.notifyIconMain.Icon = ((System.Drawing.Icon)(resources.GetObject("notifyIconMain.Icon")));
            this.notifyIconMain.Visible = true;
            this.notifyIconMain.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyIconMain_MouseDoubleClick);
            // 
            // textBoxLog
            // 
            this.textBoxLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBoxLog.Location = new System.Drawing.Point(3, 3);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Size = new System.Drawing.Size(812, 401);
            this.textBoxLog.TabIndex = 3;
            // 
            // tabControl1
            //
            this.tabControl1.Controls.Add(this.tabPageText);
            this.tabControl1.Controls.Add(this.tabPageText2);
            this.tabControl1.Controls.Add(this.tabPageText3);
            this.tabControl1.Controls.Add(this.tabPageText4);
            this.tabControl1.Controls.Add(this.tabPageText5);
            this.tabControl1.Controls.Add(this.tabPageLog);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(826, 433);
            this.tabControl1.TabIndex = 4;
            // 
            // tabPageText
            // 
            this.tabPageText.Controls.Add(this.textBox文本记录);
            this.tabPageText.Location = new System.Drawing.Point(4, 22);
            this.tabPageText.Name = "tabPageText";
            this.tabPageText.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageText.Size = new System.Drawing.Size(818, 407);
            this.tabPageText.TabIndex = 0;
            this.tabPageText.Text = "记录";
            this.tabPageText.UseVisualStyleBackColor = true;
            // 
            // textBox文本记录
            //
            this.textBox文本记录.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox文本记录.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox文本记录.Location = new System.Drawing.Point(3, 3);
            this.textBox文本记录.Multiline = true;
            this.textBox文本记录.Name = "textBox文本记录";
            this.textBox文本记录.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox文本记录.Size = new System.Drawing.Size(812, 401);
            this.textBox文本记录.TabIndex = 4;
            //
            // tabPageText2
            //
            this.tabPageText2.Controls.Add(this.textBox文本记录2);
            this.tabPageText2.Location = new System.Drawing.Point(4, 22);
            this.tabPageText2.Name = "tabPageText2";
            this.tabPageText2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageText2.Size = new System.Drawing.Size(818, 407);
            this.tabPageText2.TabIndex = 2;
            this.tabPageText2.Text = "记录2";
            this.tabPageText2.UseVisualStyleBackColor = true;
            //
            // textBox文本记录2
            //
            this.textBox文本记录2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox文本记录2.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox文本记录2.Location = new System.Drawing.Point(3, 3);
            this.textBox文本记录2.Multiline = true;
            this.textBox文本记录2.Name = "textBox文本记录2";
            this.textBox文本记录2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox文本记录2.Size = new System.Drawing.Size(812, 401);
            this.textBox文本记录2.TabIndex = 5;
            //
            // tabPageText3
            //
            this.tabPageText3.Controls.Add(this.textBox文本记录3);
            this.tabPageText3.Location = new System.Drawing.Point(4, 22);
            this.tabPageText3.Name = "tabPageText3";
            this.tabPageText3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageText3.Size = new System.Drawing.Size(818, 407);
            this.tabPageText3.TabIndex = 3;
            this.tabPageText3.Text = "记录3";
            this.tabPageText3.UseVisualStyleBackColor = true;
            //
            // textBox文本记录3
            //
            this.textBox文本记录3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox文本记录3.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox文本记录3.Location = new System.Drawing.Point(3, 3);
            this.textBox文本记录3.Multiline = true;
            this.textBox文本记录3.Name = "textBox文本记录3";
            this.textBox文本记录3.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox文本记录3.Size = new System.Drawing.Size(812, 401);
            this.textBox文本记录3.TabIndex = 6;
            //
            // tabPageText4
            //
            this.tabPageText4.Controls.Add(this.textBox文本记录4);
            this.tabPageText4.Location = new System.Drawing.Point(4, 22);
            this.tabPageText4.Name = "tabPageText4";
            this.tabPageText4.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageText4.Size = new System.Drawing.Size(818, 407);
            this.tabPageText4.TabIndex = 4;
            this.tabPageText4.Text = "记录4";
            this.tabPageText4.UseVisualStyleBackColor = true;
            //
            // textBox文本记录4
            //
            this.textBox文本记录4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox文本记录4.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox文本记录4.Location = new System.Drawing.Point(3, 3);
            this.textBox文本记录4.Multiline = true;
            this.textBox文本记录4.Name = "textBox文本记录4";
            this.textBox文本记录4.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox文本记录4.Size = new System.Drawing.Size(812, 401);
            this.textBox文本记录4.TabIndex = 7;
            //
            // tabPageText5
            //
            this.tabPageText5.Controls.Add(this.textBox文本记录5);
            this.tabPageText5.Location = new System.Drawing.Point(4, 22);
            this.tabPageText5.Name = "tabPageText5";
            this.tabPageText5.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageText5.Size = new System.Drawing.Size(818, 407);
            this.tabPageText5.TabIndex = 5;
            this.tabPageText5.Text = "记录5";
            this.tabPageText5.UseVisualStyleBackColor = true;
            //
            // textBox文本记录5
            //
            this.textBox文本记录5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox文本记录5.Font = new System.Drawing.Font("宋体", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox文本记录5.Location = new System.Drawing.Point(3, 3);
            this.textBox文本记录5.Multiline = true;
            this.textBox文本记录5.Name = "textBox文本记录5";
            this.textBox文本记录5.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox文本记录5.Size = new System.Drawing.Size(812, 401);
            this.textBox文本记录5.TabIndex = 8;
            //
            // tabPageLog
            // 
            this.tabPageLog.Controls.Add(this.textBoxLog);
            this.tabPageLog.Location = new System.Drawing.Point(4, 22);
            this.tabPageLog.Name = "tabPageLog";
            this.tabPageLog.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageLog.Size = new System.Drawing.Size(818, 407);
            this.tabPageLog.TabIndex = 6;
            this.tabPageLog.Text = "日志";
            this.tabPageLog.UseVisualStyleBackColor = true;
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(826, 433);
            this.Controls.Add(this.tabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.SizableToolWindow;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "小助手";
            this.TopMost = true;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Main_FormClosing);
            this.Load += new System.EventHandler(this.Main_Load);
            this.contextMenuStripNotifyIconMain.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPageText.ResumeLayout(false);
            this.tabPageText.PerformLayout();
            this.tabPageText2.ResumeLayout(false);
            this.tabPageText2.PerformLayout();
            this.tabPageText3.ResumeLayout(false);
            this.tabPageText3.PerformLayout();
            this.tabPageText4.ResumeLayout(false);
            this.tabPageText4.PerformLayout();
            this.tabPageText5.ResumeLayout(false);
            this.tabPageText5.PerformLayout();
            this.tabPageLog.ResumeLayout(false);
            this.tabPageLog.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ContextMenuStrip contextMenuStripNotifyIconMain;
        private System.Windows.Forms.NotifyIcon notifyIconMain;
        private System.Windows.Forms.ToolStripMenuItem ExitToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 文件复制助手ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem 邮箱附件接收助手ToolStripMenuItem;
        public TextBox textBoxLog;
        private ToolStripMenuItem 文件解压助手ToolStripMenuItem;
        private ToolStripMenuItem 网页常挂助手ToolStripMenuItem;
        private ToolStripMenuItem 网页常挂助手V2ToolStripMenuItem;
        private ToolStripMenuItem 监控Visio文件自动转PDFToolStripMenuItem;
        private ToolStripMenuItem 监控文件ToolStripMenuItem;
        private TabControl tabControl1;
        private TabPage tabPageText;
        private TabPage tabPageText2;
        private TabPage tabPageText3;
        private TabPage tabPageText4;
        private TabPage tabPageText5;
        private TabPage tabPageLog;
        public TextBox textBox文本记录;
        public TextBox textBox文本记录2;
        public TextBox textBox文本记录3;
        public TextBox textBox文本记录4;
        public TextBox textBox文本记录5;
        private ToolStripMenuItem 授权管理ToolStripMenuItem;
        private ToolStripSeparator toolStripMenuItem1;
        private ToolStripMenuItem 关于ToolStripMenuItem;
        private ToolStripMenuItem 中国铁塔照片下载助手ToolStripMenuItem;
        private ToolStripMenuItem 新建目录ToolStripMenuItem;
    }
}
